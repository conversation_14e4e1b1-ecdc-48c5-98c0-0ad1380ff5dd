import React, { useState, useEffect } from 'react';
import { Card, Button, Table, Tag, Progress, Tooltip, Alert, Spin, message, Row, Col } from 'antd';
import { ArrowLeftOutlined, StarFilled, ClockCircleOutlined, CheckCircleOutlined, StopOutlined, WarningOutlined, ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { http } from 'libs';
import styles from './index.module.less';

export default function GanttChart({ data, plan, loading }) {
  // 按测试人员分组任务
  const groupTasksByTester = (tasks) => {
    const grouped = {};
    if (!tasks) return grouped;
    for (const task of tasks) {
      if (!grouped[task.tester]) {
        grouped[task.tester] = [];
      }
      grouped[task.tester].push(task);
    }
    return grouped;
  };

  // 获取整体时间范围 - 使用测试计划的起止时间
  const getDateRange = () => {
    if (!plan || !plan.start_date) {
      // 如果没有测试计划数据，则使用当前月份作为默认值
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth(), 1);
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      return { start, end };
    }

    // 使用测试计划的起止时间
    const start = new Date(plan.start_date);
    const end = new Date(plan.end_date);
    return { start, end };
  };

  // 生成日期数组
  const generateDateRange = (startDate, endDate) => {
    const dates = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      dates.push(new Date(d).toISOString().split('T')[0]);
    }
    return dates;
  };

  // 计算任务在甘特图中的位置和宽度（更稳健，直接用日期差，避免字符串匹配问题）
  const calculateTaskPosition = (taskStart, taskEnd, ganttStart, ganttEnd) => {
    const startDate = new Date(taskStart);
    const endDate = new Date(taskEnd);
    const ganttStartDate = new Date(ganttStart);
    const ganttEndDate = new Date(ganttEnd);

    // 总天数（包含首尾，+1）
    const totalDays = Math.floor((ganttEndDate - ganttStartDate) / (1000 * 60 * 60 * 24)) + 1;

    // 任务开始、结束相对于甘特图开始的索引
    const taskStartIndex = Math.max(0, Math.floor((startDate - ganttStartDate) / (1000 * 60 * 60 * 24)));
    const taskEndIndex = Math.min(totalDays - 1, Math.floor((endDate - ganttStartDate) / (1000 * 60 * 60 * 24)));

    if (taskEndIndex < taskStartIndex) {
      return { left: '0%', width: '0%' };
    }

    const left = (taskStartIndex / totalDays) * 100;
    const width = ((taskEndIndex - taskStartIndex + 1) / totalDays) * 100;

    return { left: `${left}%`, width: `${width}%` };
  };

  // 获取状态图标
  const getStatusIcon = (status) => {
    const iconMap = {
      'pending': <ClockCircleOutlined />,
      'in_progress': <StarFilled />,
      'completed': <CheckCircleOutlined />,
      'cancelled': <StopOutlined />,
      'blocked': <StopOutlined />,
      'delayed': <ClockCircleOutlined />
    };
    return iconMap[status] || <ClockCircleOutlined />;
  };

  // 颜色系统：红橙黄绿青蓝紫
  const COLOR_SYSTEMS = [
    {
      name: 'red',
      base: '#ef4444',
      colors: {
        pending: '#fca5a5',     // 浅红
        in_progress: '#ef4444', // 标准红
        completed: '#dc2626',   // 深红
        cancelled: '#991b1b',   // 很深红
        blocked: '#b91c1c',     // 深红
        delayed: '#f87171'      // 中红
      }
    },
    {
      name: 'orange',
      base: '#f97316',
      colors: {
        pending: '#fed7aa',     // 浅橙
        in_progress: '#f97316', // 标准橙
        completed: '#ea580c',   // 深橙
        cancelled: '#9a3412',   // 很深橙
        blocked: '#c2410c',     // 深橙
        delayed: '#fb923c'      // 中橙
      }
    },
    {
      name: 'yellow',
      base: '#eab308',
      colors: {
        pending: '#fde68a',     // 浅黄
        in_progress: '#eab308', // 标准黄
        completed: '#ca8a04',   // 深黄
        cancelled: '#713f12',   // 很深黄
        blocked: '#a16207',     // 深黄
        delayed: '#facc15'      // 中黄
      }
    },
    {
      name: 'green',
      base: '#22c55e',
      colors: {
        pending: '#bbf7d0',     // 浅绿
        in_progress: '#22c55e', // 标准绿
        completed: '#16a34a',   // 深绿
        cancelled: '#14532d',   // 很深绿
        blocked: '#166534',     // 深绿
        delayed: '#4ade80'      // 中绿
      }
    },
    {
      name: 'cyan',
      base: '#06b6d4',
      colors: {
        pending: '#a5f3fc',     // 浅青
        in_progress: '#06b6d4', // 标准青
        completed: '#0891b2',   // 深青
        cancelled: '#164e63',   // 很深青
        blocked: '#0e7490',     // 深青
        delayed: '#22d3ee'      // 中青
      }
    },
    {
      name: 'blue',
      base: '#3b82f6',
      colors: {
        pending: '#bfdbfe',     // 浅蓝
        in_progress: '#3b82f6', // 标准蓝
        completed: '#2563eb',   // 深蓝
        cancelled: '#1e3a8a',   // 很深蓝
        blocked: '#1d4ed8',     // 深蓝
        delayed: '#60a5fa'      // 中蓝
      }
    },
    {
      name: 'purple',
      base: '#8b5cf6',
      colors: {
        pending: '#ddd6fe',     // 浅紫
        in_progress: '#8b5cf6', // 标准紫
        completed: '#7c3aed',   // 深紫
        cancelled: '#581c87',   // 很深紫
        blocked: '#6d28d9',     // 深紫
        delayed: '#a78bfa'      // 中紫
      }
    }
  ];

  // 根据测试人员索引获取颜色系统
  const getColorSystemByTesterIndex = (index) => {
    return COLOR_SYSTEMS[index % COLOR_SYSTEMS.length];
  };

  // 根据状态获取颜色
  const getTaskColor = (status, testerIndex) => {
    const colorSystem = getColorSystemByTesterIndex(testerIndex);
    return colorSystem.colors[status] || colorSystem.colors.pending;
  };

  // 根据基础色和状态获取最终颜色
  const getTaskStatusInfo = (status, baseColor) => {
    const statusTextMap = {
      'pending': '待开始', 'in_progress': '进行中', 'completed': '已完成',
      'cancelled': '已取消', 'blocked': '阻塞中', 'delayed': '已延期'
    };
    const text = statusTextMap[status] || '未知状态';
    return { text };
  };

  // 计算风险信息
  const calculateRiskInfo = (tasks) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const risks = {
      overdue: 0,           // 超期任务
      urgentDeadline: 0,    // 紧急截止（3天内）
      highRiskProgress: 0,  // 高风险进度（进度<50%且剩余时间<30%）
      blockedTasks: 0,      // 阻塞任务
      delayedTasks: 0,      // 延期任务
      resourceConflict: 0   // 资源冲突（同一人同时有多个进行中任务）
    };

    const testerWorkload = {};

    tasks.forEach(task => {
      const endDate = new Date(task.end_date);
      const startDate = new Date(task.start_date);
      const totalDuration = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
      const remainingDays = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));
      
      // 超期任务
      if (endDate < today && task.test_status !== 'completed') {
        risks.overdue++;
      }
      
      // 紧急截止
      if (remainingDays <= 3 && remainingDays > 0 && task.test_status !== 'completed') {
        risks.urgentDeadline++;
      }
      
      // 高风险进度
      if (task.progress < 50 && remainingDays > 0 && remainingDays / totalDuration < 0.3) {
        risks.highRiskProgress++;
      }
      
      // 阻塞和延期任务
      if (task.test_status === 'blocked') {
        risks.blockedTasks++;
      }
      if (task.test_status === 'delayed') {
        risks.delayedTasks++;
      }
      
      // 资源冲突统计
      if (task.test_status === 'in_progress') {
        testerWorkload[task.tester] = (testerWorkload[task.tester] || 0) + 1;
      }
    });

    // 计算资源冲突
    Object.values(testerWorkload).forEach(count => {
      if (count > 2) { // 同时进行超过2个任务视为冲突
        risks.resourceConflict += count - 2;
      }
    });

    return risks;
  };

  // 风险提示组件
  const RiskAlert = ({ data }) => {
    const risks = calculateRiskInfo(data);
    const totalRisks = Object.values(risks).reduce((sum, count) => sum + count, 0);

    if (totalRisks === 0) {
      return (
        <div className={styles.riskAlert}>
          <div className={styles.riskHeader}>
            <CheckCircleOutlined className={styles.riskIcon} style={{ color: '#22c55e' }} />
            <span className={styles.riskTitle}>项目状态良好</span>
          </div>
          <div className={styles.riskContent}>
            <span>当前没有发现风险项目，项目进展顺利</span>
          </div>
        </div>
      );
    }

    const getRiskLevel = (totalRisks) => {
      if (totalRisks >= 10) return { level: 'high', color: '#ef4444', icon: <ExclamationCircleOutlined /> };
      if (totalRisks >= 5) return { level: 'medium', color: '#f97316', icon: <WarningOutlined /> };
      return { level: 'low', color: '#eab308', icon: <InfoCircleOutlined /> };
    };

    const riskLevel = getRiskLevel(totalRisks);

    return (
      <div className={`${styles.riskAlert} ${styles[`risk-${riskLevel.level}`]}`}>
        <div className={styles.riskHeader}>
          <span className={styles.riskIcon} style={{ color: riskLevel.color }}>
            {riskLevel.icon}
          </span>
          <span className={styles.riskTitle}>
            发现 {totalRisks} 个风险项
          </span>
        </div>
        <div className={styles.riskContent}>
          <Row gutter={[16, 8]}>
            {risks.overdue > 0 && (
              <Col span={8}>
                <div className={styles.riskItem}>
                  <span className={styles.riskCount}>{risks.overdue}</span>
                  <span className={styles.riskLabel}>任务超期</span>
                </div>
              </Col>
            )}
            {risks.urgentDeadline > 0 && (
              <Col span={8}>
                <div className={styles.riskItem}>
                  <span className={styles.riskCount}>{risks.urgentDeadline}</span>
                  <span className={styles.riskLabel}>紧急截止</span>
                </div>
              </Col>
            )}
            {risks.highRiskProgress > 0 && (
              <Col span={8}>
                <div className={styles.riskItem}>
                  <span className={styles.riskCount}>{risks.highRiskProgress}</span>
                  <span className={styles.riskLabel}>进度风险</span>
                </div>
              </Col>
            )}
            {risks.blockedTasks > 0 && (
              <Col span={8}>
                <div className={styles.riskItem}>
                  <span className={styles.riskCount}>{risks.blockedTasks}</span>
                  <span className={styles.riskLabel}>任务阻塞</span>
                </div>
              </Col>
            )}
            {risks.delayedTasks > 0 && (
              <Col span={8}>
                <div className={styles.riskItem}>
                  <span className={styles.riskCount}>{risks.delayedTasks}</span>
                  <span className={styles.riskLabel}>任务延期</span>
                </div>
              </Col>
            )}
            {risks.resourceConflict > 0 && (
              <Col span={8}>
                <div className={styles.riskItem}>
                  <span className={styles.riskCount}>{risks.resourceConflict}</span>
                  <span className={styles.riskLabel}>资源冲突</span>
                </div>
              </Col>
            )}
          </Row>
        </div>
      </div>
    );
  };

  // 甘特图行组件
  const GanttRow = ({ tester, tasks, ganttStart, ganttEnd, index }) => {
    const ganttDates = generateDateRange(ganttStart, ganttEnd);
    const today = new Date().toISOString().split('T')[0];
    const rowHeight = Math.max(tasks.length * 40 + 20, 80);
    const colorSystem = getColorSystemByTesterIndex(index);

    return (
      <div className={styles.ganttRow} style={{ height: `${rowHeight}px` }}>
        <div className={styles.ganttRowHeader}>
          <div>
            <strong>{tester}</strong>
            <div style={{ fontSize: '12px', color: 'rgba(255,255,255,0.8)', marginTop: '4px' }}>
              {tasks.length} 个任务
            </div>
            <div 
              className={styles.testerColorIndicator}
              style={{ backgroundColor: colorSystem.base }}
            />
          </div>
        </div>
        <div className={styles.ganttRowContent}>
          {/* 背景日期网格 */}
          {ganttDates.map((date, dateIndex) => (
            <div
              key={date}
              className={`${styles.dateGrid} ${date === today ? styles.today : ''}`}
              style={{
                left: `${(dateIndex / ganttDates.length) * 100}%`,
                width: `${100 / ganttDates.length}%`,
              }}
            />
          ))}

          {/* 任务条 */}
          {tasks.map((task, taskIndex) => {
            const position = calculateTaskPosition(task.start_date, task.end_date, ganttStart, ganttEnd);
            const statusInfo = getTaskStatusInfo(task.test_status);
            const top = taskIndex * 40 + 10;
            const taskColor = getTaskColor(task.test_status, index);
            const statusIcon = getStatusIcon(task.test_status);

            // 判断是否有风险
            const endDate = new Date(task.end_date);
            const todayDate = new Date();
            const isOverdue = endDate < todayDate && task.test_status !== 'completed';
            const isUrgent = Math.ceil((endDate - todayDate) / (1000 * 60 * 60 * 24)) <= 3;

            return (
              <Tooltip
                key={task.id}
                title={
                  <div className={styles.taskTooltip}>
                    <div className={styles.taskTitle}>
                      {statusIcon} {task.model_name}
                    </div>
                    <div><strong>类型:</strong> <span>{task.model_type_display}</span></div>
                    <div><strong>GPU型号:</strong> <span>{task.gpu_model_display}</span></div>
                    <div><strong>时间:</strong> <span>{task.start_date} ~ {task.end_date}</span></div>
                    <div><strong>优先级:</strong> <span>{task.priority_display}</span></div>
                    <div><strong>状态:</strong> <span>{statusInfo.text}</span></div>
                    <div><strong>进度:</strong> <span>{task.progress}%</span></div>
                    <div><strong>资料输出:</strong> <span style={{ color: task.document_output ? '#10b981' : '#6b7280' }}>
                      {task.document_output ? '✅ 已输出' : '❌ 未输出'}
                    </span></div>
                    {task.test_case_set_info && (
                      <div><strong>关联用例集:</strong> <span style={{ color: '#1890ff' }}>
                        {task.test_case_set_info.name}
                        {task.test_case_set_info.test_cases_count > 0 && ` (${task.test_case_set_info.test_cases_count}个用例)`}
                      </span></div>
                    )}
                    {task.notes && <div><strong>备注:</strong> <span>{task.notes}</span></div>}
                    {isOverdue && <div style={{ color: '#ef4444', fontWeight: 'bold' }}>⚠️ 任务已超期</div>}
                    {isUrgent && !isOverdue && <div style={{ color: '#f97316', fontWeight: 'bold' }}>🔥 紧急截止</div>}
                  </div>
                }
                placement="topLeft"
                overlayStyle={{ maxWidth: '300px' }}
                overlayClassName={styles.taskTooltipOverlay}
              >
                <div
                  className={`${styles.taskBar} ${isOverdue ? styles.overdueTask : ''} ${isUrgent ? styles.urgentTask : ''}`}
                  style={{
                    top: `${top}px`,
                    left: position.left,
                    width: position.width,
                    minWidth: '60px',
                    backgroundColor: taskColor
                  }}
                  onClick={() => task.onTaskClick && task.onTaskClick(task)}
                >
                  <div
                    className={styles.taskProgressBar}
                    style={{ width: `${task.progress}%` }}
                    data-progress={task.progress}
                  />
                  <span className={styles.taskName}>
                    {task.model_name}
                    {/* 资料输出标记 - 改为圆球样式 */}
                    {task.document_output && (
                      <span 
                        className={styles.documentOutputCircle}
                        title="已输出资料文档"
                      >
                        资
                      </span>
                    )}
                  </span>
                  {/* 优先级指示器 */}
                  <div className={`${styles.priorityIndicator} ${styles[`priority-${task.priority}`]}`} />
                  {/* 风险标识 */}
                  {isOverdue && <div className={styles.riskIndicator}>!</div>}
                </div>
              </Tooltip>
            );
          })}
        </div>
      </div>
    );
  };

  // 甘特图头部（日期）
  const GanttHeader = ({ startDate, endDate }) => {
    const dates = generateDateRange(startDate, endDate);
    const months = {};
    
    // 计算每个月占用的天数
    dates.forEach(date => {
      const month = date.substring(0, 7); // YYYY-MM
      if (!months[month]) {
        months[month] = 1;
      } else {
        months[month]++;
      }
    });

    // 格式化月份显示
    const formatMonth = (monthStr) => {
      const [year, month] = monthStr.split('-');
      return `${year}年${parseInt(month)}月`;
    };
    
    return (
      <div className={styles.ganttHeader}>
        <div className={styles.ganttHeaderLeft}>
          测试人员
        </div>
        <div className={styles.ganttHeaderRight}>
          {/* 月份行 */}
          <div className={styles.monthRow}>
            {Object.entries(months).map(([month, days]) => (
              <div
                key={month}
                style={{ width: `${(days / dates.length) * 100}%` }}
                className={styles.monthCell}
              >
                {formatMonth(month)}
              </div>
            ))}
          </div>
          {/* 日期行 */}
          <div className={styles.dateRow}>
            {dates.map((date, index) => {
              const dayOfWeek = new Date(date).getDay();
              const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
              const isToday = date === new Date().toISOString().split('T')[0];
              
              return (
                <div 
                  key={date}
                  className={`${styles.dateCell} ${isWeekend ? styles.weekend : ''} ${isToday ? styles.todayHeader : ''}`}
                  style={{ width: `${100 / dates.length}%` }}
                >
                  <div>{new Date(date).getDate()}</div>
                  <div style={{ fontSize: '10px', opacity: 0.7 }}>
                    {['日', '一', '二', '三', '四', '五', '六'][dayOfWeek]}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  // 计算统计信息
  const getStatistics = (tasks) => {
    const stats = {
      total: tasks.length,
      pending: 0,
      in_progress: 0,
      completed: 0,
      cancelled: 0,
      blocked: 0,
      delayed: 0,
      avgProgress: 0,
      totalTesters: 0
    };

    tasks.forEach(task => {
      stats[task.test_status] = (stats[task.test_status] || 0) + 1;
      stats.avgProgress += task.progress || 0;
    });

    stats.avgProgress = Math.round(stats.avgProgress / tasks.length);
    stats.totalTesters = Object.keys(groupTasksByTester(tasks)).length;

    return stats;
  };

  // 统计信息组件
  const StatisticsCard = ({ data }) => {
    const stats = getStatistics(data);
    
    return (
      <div className={styles.statsCard}>
        <div className={styles.statsTitle}>项目统计</div>
        <div className={styles.statsGrid}>
          <div className={styles.statItem}>
            <span className={styles.statValue}>{stats.total}</span>
            <span className={styles.statLabel}>总任务</span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statValue}>{stats.totalTesters}</span>
            <span className={styles.statLabel}>测试人员</span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statValue}>{stats.completed}</span>
            <span className={styles.statLabel}>已完成</span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statValue}>{stats.in_progress}</span>
            <span className={styles.statLabel}>进行中</span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statValue}>{stats.avgProgress}%</span>
            <span className={styles.statLabel}>平均进度</span>
          </div>
        </div>
      </div>
    );
  };

  // 图例组件
  const Legend = ({ data }) => {
    const groupedTasks = groupTasksByTester(data);
    const testers = Object.keys(groupedTasks);

    return (
      <div className={styles.legend}>
        <div className={styles.legendSection}>
          <span className={styles.legendSectionTitle}>测试人员:</span>
          {testers.map((tester, index) => {
            const colorSystem = getColorSystemByTesterIndex(index);
            return (
              <div key={tester} className={styles.legendItem}>
                <div 
                  className={styles.legendColor} 
                  style={{ background: colorSystem.base }}
                />
                <span>{tester}</span>
              </div>
            );
          })}
        </div>
        <div className={styles.legendSection}>
          <span className={styles.legendSectionTitle}>任务状态:</span>
          <div className={styles.legendItem}>
            <div className={styles.legendColor} style={{ background: '#bfdbfe' }} />
            <span>待开始</span>
          </div>
          <div className={styles.legendItem}>
            <div className={styles.legendColor} style={{ background: '#3b82f6' }} />
            <span>进行中</span>
          </div>
          <div className={styles.legendItem}>
            <div className={styles.legendColor} style={{ background: '#1e40af' }} />
            <span>已完成</span>
          </div>
        </div>
        <div className={styles.legendSection}>
          <span className={styles.legendSectionTitle}>特殊标识:</span>
          <div className={styles.legendItem}>
            <div className={styles.legendColor} style={{ background: '#3b82f6' }} />
            <span>今天</span>
          </div>
          <div className={styles.legendItem}>
            <div className={styles.legendColor} style={{ background: '#ef4444', color: '#fff', fontSize: '10px', fontWeight: 'bold' }}>!</div>
            <span>风险任务</span>
          </div>
          <div className={styles.legendItem}>
            <div 
              style={{ 
                width: '16px',
                height: '16px',
                borderRadius: '50%',
                backgroundColor: '#10b981',
                color: 'white',
                fontSize: '9px',
                fontWeight: 'bold',
                textAlign: 'center',
                lineHeight: '16px',
                marginRight: '4px'
              }}
            >
              资
            </div>
            <span>资料已输出</span>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={styles.loading}>
        <Spin size="large" tip="加载甘特图数据中..." />
      </div>
    );
  }

  if (!data?.length) {
    return (
      <Alert
        message="暂无任务数据"
        description="请添加新的测试任务或导入任务数据来查看甘特图"
        type="info"
        showIcon
        style={{ 
          margin: '20px 0',
          borderRadius: '12px',
          border: '1px solid #e2e8f0'
        }}
      />
    );
  }

  const groupedTasks = groupTasksByTester(data);
  const dateRange = getDateRange();

  return (
    <div>
      <RiskAlert data={data} />
      <StatisticsCard data={data} />
      <Legend data={data} />
      <div className={styles.ganttContainer}>
        <GanttHeader startDate={dateRange.start} endDate={dateRange.end} />
        {Object.entries(groupedTasks).map(([tester, tasks], index) => (
          <GanttRow
            key={tester}
            tester={tester}
            tasks={tasks}
            ganttStart={dateRange.start}
            ganttEnd={dateRange.end}
            index={index}
          />
        ))}
      </div>
    </div>
  );
} 