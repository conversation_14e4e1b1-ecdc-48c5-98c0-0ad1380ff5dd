# Spug 项目目录结构说明

## 📁 整理后的目录结构

```
spug/
├── 📄 README.md                    # 项目主要说明文档
├── 📄 LICENSE                      # 开源许可证
├── 📄 .gitignore                   # Git忽略文件配置
│
├── 📁 spug_api/                    # 🔧 后端API服务 (Django)
│   ├── apps/                       # Django应用模块
│   ├── spug/                       # 项目配置
│   ├── manage.py                   # Django管理脚本
│   ├── requirements.txt            # Python依赖
│   └── ...
│
├── 📁 spug_web/                    # 🎨 前端界面 (React)
│   ├── src/                        # 源代码
│   ├── public/                     # 静态资源
│   ├── package.json                # Node.js依赖
│   └── ...
│
├── 📁 docs/                        # 📚 文档目录
│   ├── 📁 api/                     # API接口文档
│   │   ├── API_DOCUMENTATION.md    # 完整API文档
│   │   └── 发布计划API对接文档.md   # 发布计划API
│   │
│   ├── 📁 user-manual/             # 用户使用手册
│   │   ├── model-storage-user-manual.html      # 模型存储手册
│   │   ├── test-plan-user-manual.html          # 测试计划手册
│   │   └── 文件详细对比功能说明.md              # 文件对比说明
│   │
│   ├── 📁 development/             # 开发相关文档
│   │   ├── BACKEND_LIBRARIES.md    # 后端依赖库说明
│   │   ├── COMPONENT_REFERENCE.md  # 前端组件参考
│   │   ├── QUICK_START_GUIDE.md    # 快速开发指南
│   │   ├── README_DOCUMENTATION.md # 项目说明文档
│   │   ├── 项目结构.md              # 详细项目结构
│   │   └── Tread.md                # 线程相关文档
│   │
│   ├── 📁 docker/                  # Docker相关配置
│   ├── FQA.md                      # 常见问题
│   └── install.sh                  # 安装脚本
│
├── 📁 scripts/                     # 🔧 脚本文件
│   └── start.ps1                   # 项目启动脚本
│
├── 📁 config/                      # ⚙️ 配置文件
│   ├── funboost_config.py          # Funboost配置
│   ├── nb_log_config.py            # 日志配置
│   └── funboost_cli_user.py        # Funboost CLI配置
│
├── 📁 assets/                      # 🖼️ 图片和静态资源
│   ├── 任务执行.png
│   ├── 引入命令.png
│   ├── 执行记录.png
│   ├── 测试计划-编辑页面.png
│   ├── 测试计划.png
│   ├── 测试计划变量配置.png
│   └── 结果展示.png
│
├── 📁 temp/                        # 🗂️ 临时文件和测试文件
│   ├── quick_test.py               # 快速测试脚本
│   └── 导入模板测试.xlsx           # 导入功能测试文件
│
└── 📁 logs/                        # 📝 日志文件 (运行时生成)
    ├── 2025-06-11.0001.funboost.log
    ├── 2025-06-11.0001.root.log
    └── ...
```

## 🔄 整理前后对比

### 整理前的问题
- ❌ 根目录文件混乱，各种类型文件混在一起
- ❌ 文档分散，难以查找
- ❌ 配置文件和脚本没有分类
- ❌ 图片资源散落在根目录
- ❌ 临时文件和正式文件混合

### 整理后的优势
- ✅ 目录结构清晰，按功能分类
- ✅ 文档集中管理，便于维护
- ✅ 配置文件统一存放
- ✅ 资源文件有序组织
- ✅ 临时文件独立管理

## 📋 各目录说明

### `/docs/` - 文档目录
- **api/**: API接口相关文档
- **user-manual/**: 面向最终用户的使用手册
- **development/**: 面向开发者的技术文档
- **docker/**: Docker部署相关文件

### `/scripts/` - 脚本目录
- 存放项目启动、部署、维护等脚本
- 目前包含 `start.ps1` 启动脚本

### `/config/` - 配置目录
- 存放各种配置文件
- 包含日志配置、第三方库配置等

### `/assets/` - 资源目录
- 存放项目相关的图片、截图等静态资源
- 主要用于文档说明和演示

### `/temp/` - 临时目录
- 存放测试文件、临时脚本等
- 可定期清理，不影响项目运行

## 🔧 维护建议

1. **定期清理**: `temp/` 和 `logs/` 目录可定期清理
2. **文档更新**: 新增功能时及时更新相应文档
3. **资源管理**: 新增截图等资源文件放入 `assets/` 目录
4. **配置管理**: 新增配置文件放入 `config/` 目录
5. **脚本管理**: 新增脚本文件放入 `scripts/` 目录

## 📝 注意事项

- `.gitignore` 已更新，会自动忽略临时文件和日志
- 原有的功能和路径引用不受影响
- 如需引用移动后的文件，请更新相应的路径配置
