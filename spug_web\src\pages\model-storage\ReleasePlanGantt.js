import React, { useState, useEffect } from 'react';
import { Card, Button, Table, Modal, Tag, Popconfirm, message, Upload, Space, Divider } from 'antd';
import { ArrowLeftOutlined, PlusOutlined, UploadOutlined, Bar<PERSON>hartOutlined, CalendarOutlined, TeamOutlined, DownloadOutlined, FileExcelOutlined, FileTextOutlined, SendOutlined, WechatOutlined } from '@ant-design/icons';
import { http, history } from 'libs';
import { useParams } from 'react-router-dom';
import styles from './index.module.less';
import GanttChart from './GanttChart';
import TestTaskForm from './TestTaskForm';
import TestStepsSidebar from '../exec/test-case-sets/TestStepsSidebar';
import TaskAssignModal from './TaskAssignModal';
import WeChatConfigModal from './WeChatConfigModal';

export default function ReleasePlanGantt() {
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [plan, setPlan] = useState({});
  const [formVisible, setFormVisible] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [exportLoading, setExportLoading] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [selectedCaseSet, setSelectedCaseSet] = useState(null);
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [assigningTask, setAssigningTask] = useState(null);
  const [wechatConfigVisible, setWechatConfigVisible] = useState(false);

  useEffect(() => {
    fetchData();
  }, [id]);

  const fetchData = () => {
    setLoading(true);
    http.get(`/api/model-storage/gantt-data/?release_plan_id=${id}`)
      .then(res => {
        // 从res.data中获取数据，因为后端返回的是 {data: gantt_data, error: ''}
        const responseData = res.data || res;
        const tasks_by_tester = responseData.tasks_by_tester || {};
        const allTasks = Object.entries(tasks_by_tester).flatMap(([tester, tasks]) => {
          return tasks.map(task => ({ ...task, tester }));
        });
        setData(allTasks);
        setPlan(responseData.release_plan || responseData.plan || {});
      })
      .finally(() => setLoading(false));
  };

  // 处理测试用例集点击
  const handleCaseSetClick = async (task) => {
    if (!task.test_case_set_info) {
      message.warning('该任务未关联测试用例集');
      return;
    }

    try {
      // 获取完整的测试用例集信息
      const response = await http.get(`/api/exec/test-case-sets/${task.test_case_set_info.id}/`);
      const caseSetData = response.data || response;

      // 将任务的步骤完成状态和进度合并到用例集数据中
      const mergedData = {
        ...caseSetData,
        step_completion_status: task.step_completion_status || {},
        progress: task.progress || 0,
        task_id: task.id  // 保存任务ID用于更新
      };

      setSelectedCaseSet(mergedData);
      setSidebarVisible(true);
    } catch (error) {
      console.error('获取测试用例集详情失败:', error);
      message.error('获取测试用例集详情失败');
    }
  };

  // 处理侧边栏更新
  const handleSidebarUpdate = (updatedData) => {
    setSelectedCaseSet(updatedData);
    // 刷新任务列表以显示更新后的进度
    fetchData();
  };

  // 处理任务分配
  const handleAssignTask = (task) => {
    setAssigningTask(task);
    setAssignModalVisible(true);
  };

  // 处理任务分配确认
  const handleAssignConfirm = (assignData) => {
    console.log('任务分配成功:', assignData);
    setAssignModalVisible(false);
    setAssigningTask(null);
    // 这里可以刷新数据或显示成功消息
    message.success('任务分配成功！');
  };

  // 处理任务分配取消
  const handleAssignCancel = () => {
    setAssignModalVisible(false);
    setAssigningTask(null);
  };
  
  // 新增：周报导出功能
  const handleExportWeeklyReport = () => {
    setExportLoading(true);
    message.loading('正在生成HTML周报，请稍候...', 0);

    // 直接下载HTML文件，传递发布计划ID
    const link = document.createElement('a');
    link.href = `/api/model-storage/weekly-report/?release_plan_id=${id}`;
    link.download = `模型测试周报_${new Date().toISOString().split('T')[0].replace(/-/g, '')}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 延迟关闭loading
    setTimeout(() => {
      message.destroy();
      setExportLoading(false);
      message.success('HTML周报导出成功！可用浏览器打开查看');
    }, 2000);
  };

  const handleImport = ({ file }) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('release_plan_id', id);
    
    setLoading(true);
    http.post('/api/model-storage/import-tasks/', formData)
      .then(res => {
        message.success(res.message);
        // 如果有详细信息，显示导入结果
        if (res.details) {
          const { created, updated, errors, error_messages } = res.details;
          if (errors > 0) {
            Modal.warning({
              title: '导入完成但有错误',
              content: (
                <div>
                  <p>成功导入：新增 {created} 条，更新 {updated} 条</p>
                  <p>错误：{errors} 条</p>
                  {error_messages && error_messages.length > 0 && (
                    <div style={{ marginTop: 8 }}>
                      <p>错误详情：</p>
                      <ul style={{ margin: 0, paddingLeft: 20 }}>
                        {error_messages.map((err, index) => (
                          <li key={index} style={{ color: '#ff4d4f' }}>{err}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ),
              width: 600
            });
          }
        }
        fetchData();
      })
      .catch(err => {
        message.error(err.message || '导入失败');
      })
      .finally(() => setLoading(false));
  };

  const handleDownloadTemplate = async () => {
    // 使用Fetch API下载导入模板
    try {
      message.loading('正在下载模板...', 0);
      const response = await fetch('/api/model-storage/import-tasks/', {
        method: 'GET',
        headers: {
          'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'X-Token': localStorage.getItem('token')
        }
      });
      
      if (!response.ok) {
        throw new Error(`下载失败: ${response.status}`);
      }
      
      // 获取blob数据
      const blob = await response.blob();
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      
      // 从Content-Disposition中提取文件名，如果没有则使用默认名称
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = '测试任务导入模板.xlsx';
      if (contentDisposition) {
        const filenameMatch = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(contentDisposition);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      }
      
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      message.destroy();
      message.success('模板下载成功');
    } catch (error) {
      message.destroy();
      message.error('下载模板失败: ' + (error.message || '未知错误'));
    }
  };

  const handleFormCancel = () => {
    setFormVisible(false);
    setEditingTask(null);
  };

  const handleFormSubmit = (formData) => {
    const payload = {
      ...formData,
      release_plan: id,
    };
    
    let request;
    if (editingTask) {
      request = http.patch(`/api/model-storage/test-tasks/${editingTask.id}/`, payload);
    } else {
      request = http.post('/api/model-storage/test-tasks/', payload);
    }
    
    request.then(() => {
      message.success(editingTask ? '更新成功' : '新增成功');
      handleFormCancel();
      fetchData();
    });
  };

  const handleDelete = (task) => {
    http.delete(`/api/model-storage/test-tasks/${task.id}/`).then(() => {
      message.success('删除成功');
      fetchData();
    });
  };

  const columns = [
    { 
      title: '模型名称', 
      dataIndex: 'model_name',
      ellipsis: true,
      width: 200
    },
    { 
      title: '测试人员', 
      dataIndex: 'tester',
      width: 120,
      render: (text) => (
        <Tag icon={<TeamOutlined />} color="blue">{text}</Tag>
      )
    },
    { 
      title: '起止日期', 
      dataIndex: 'start_date', 
      width: 200,
      render: (_, record) => (
        <span>
          <CalendarOutlined style={{ marginRight: 4 }} />
          {record.start_date} ~ {record.end_date}
        </span>
      )
    },
    {
      title: '优先级',
      dataIndex: 'priority_display',
      width: 100,
      render: (text, record) => {
        const colorMap = {
          'p1': 'red',
          'p2': 'orange',
          'p3': 'green'
        };
        const displayMap = {
          'p1': 'P1-高',
          'p2': 'P2-中',
          'p3': 'P3-低'
        };
        return <Tag color={colorMap[record.priority] || 'default'}>
          {displayMap[record.priority] || record.priority_display}
        </Tag>;
            }
          },
          {
            title: '关联用例集',
            dataIndex: 'test_case_set_info',
            width: 150,
            render: (testCaseSetInfo, record) => {
        if (testCaseSetInfo && testCaseSetInfo.name) {
          const caseCount = testCaseSetInfo.test_cases_count || 0;
          return (
            <div
              style={{
          cursor: 'pointer',
          display: 'inline-block',
          borderRadius: 4,
          boxShadow: '0 0 0 2px #1890ff33',
          transition: 'box-shadow 0.2s',
          outline: 'none'
              }}
              tabIndex={0}
              onClick={() => handleCaseSetClick(record)}
              onKeyPress={e => {
          if (e.key === 'Enter' || e.key === ' ') handleCaseSetClick(record);
              }}
              title="点击查看用例集详情"
            >
              <Tag
          color="blue"
          icon={<FileTextOutlined />}
          style={{
            cursor: 'pointer',
            fontWeight: 600,
            fontSize: 14,
            background: '#e6f7ff',
            border: '1px solid #1890ff',
            padding: '4px 12px',
            margin: 0
          }}
              >
          <span style={{ textDecoration: 'underline', textUnderlineOffset: 2 }}>
            {testCaseSetInfo.name}
            {caseCount > 0 && ` (${caseCount}个用例)`}
          </span>
              </Tag>
            </div>
          );
        }
        return <span style={{ color: '#999', fontSize: '16px' }}>未关联</span>;
            }
          },
          { 
            title: '状态', 
            dataIndex: 'test_status_display', 
            width: 100,
            render: (text, record) => {
        const colorMap = {
          'pending': 'gold',
          'in_progress': 'blue',
          'completed': 'green',
          'cancelled': 'default',
          'blocked': 'red',
          'delayed': 'orange'
        };
        return <Tag color={colorMap[record.test_status] || 'default'}>{text}</Tag>;
      }
    },
    { 
      title: '进度', 
      dataIndex: 'progress', 
      width: 100,
      render: (text) => (
        <span style={{ 
          color: text >= 80 ? '#52c41a' : text >= 50 ? '#faad14' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {text}%
        </span>
      )
    },
    {
      title: '操作',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            onClick={() => {
              setEditingTask(record);
              setFormVisible(true);
            }}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<SendOutlined />}
            onClick={() => handleAssignTask(record)}
            style={{ color: '#1890ff' }}
          >
            分配
          </Button>
          <Popconfirm
            title={`确定要删除任务【${record.model_name}】吗？`}
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger size="small">删除</Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '0 24px' }}>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Button 
              type="text" 
              icon={<ArrowLeftOutlined />} 
              onClick={() => history.goBack()}
              style={{ padding: '4px 8px' }}
            />
            <BarChartOutlined style={{ color: '#1890ff' }} />
            <span style={{ fontSize: '18px', fontWeight: 'bold' }}>
              {plan.name || '发布计划甘特图'}
            </span>
          </div>
        }
        extra={
          <Space>
            <Button 
              type="default" 
              icon={<FileExcelOutlined />}
              onClick={handleExportWeeklyReport}
              loading={exportLoading}
              style={{ 
                color: '#52c41a',
                borderColor: '#52c41a'
              }}
            >
              导出周报
            </Button>
            <Button
              type="default"
              icon={<DownloadOutlined />}
              onClick={handleDownloadTemplate}
            >
              下载模板
            </Button>
            <Button
              type="default"
              icon={<WechatOutlined />}
              onClick={() => setWechatConfigVisible(true)}
              style={{
                color: '#1890ff',
                borderColor: '#1890ff'
              }}
            >
              企业微信配置
            </Button>
            <Upload
              name="file"
              showUploadList={false}
              customRequest={handleImport}
              beforeUpload={file => {
                const isXlsx = file.name.endsWith('.xlsx') || file.name.endsWith('.xls');
                if (!isXlsx) {
                  message.error('只能上传 .xlsx 或 .xls 格式的文件');
                }
                return isXlsx;
              }}
            >
              <Button icon={<UploadOutlined />}>
                从Excel导入
              </Button>
            </Upload>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={() => {
                setEditingTask(null);
                setFormVisible(true);
              }}
            >
              新增任务
            </Button>
          </Space>
        }
        style={{ 
          marginBottom: '24px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}
      >
        <GanttChart data={data} plan={plan} loading={loading} />
      </Card>
      
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <TeamOutlined style={{ color: '#1890ff' }} />
            <span>任务详情列表</span>
          </div>
        }
        style={{ 
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}
      >
        <Table
          loading={loading}
          columns={columns}
          dataSource={data}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSize: 10,
            pageSizeOptions: ['10', '20', '50', '100']
          }}
          scroll={{ x: 1000 }}
          size="middle"
        />
      </Card>

      {formVisible && (
        <TestTaskForm
          visible={formVisible}
          initialValues={editingTask}
          onCancel={handleFormCancel}
          onOk={handleFormSubmit}
        />
      )}

      <TestStepsSidebar
        visible={sidebarVisible}
        onClose={() => setSidebarVisible(false)}
        caseSet={selectedCaseSet}
        onUpdate={handleSidebarUpdate}
        taskId={selectedCaseSet?.task_id}
        mode="task"
      />

      <TaskAssignModal
        visible={assignModalVisible}
        task={assigningTask}
        onOk={handleAssignConfirm}
        onCancel={handleAssignCancel}
      />

      <WeChatConfigModal
        visible={wechatConfigVisible}
        onCancel={() => setWechatConfigVisible(false)}
      />
    </div>
  );
}