import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, DatePicker, Select, InputNumber, Switch } from 'antd';
import moment from 'moment';
import http from 'libs/http';
import UserSelector from 'components/UserSelector';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

export default function TestTaskForm({ visible, initialValues, onCancel, onOk }) {
  const [form] = Form.useForm();
  const [gpuOptions, setGpuOptions] = useState([]);
  const [testCaseSetOptions, setTestCaseSetOptions] = useState([]);

  const handleOk = () => {
    console.log('=== 表单提交开始 ===');
    
    form.validateFields()
      .then(values => {
        console.log('表单验证通过，原始values:', values);
        
        const data = { ...values };
        console.log('复制后的data:', data);
        
        if (values.date_range && values.date_range.length === 2) {
          data.start_date = values.date_range[0].format('YYYY-MM-DD');
          data.end_date = values.date_range[1].format('YYYY-MM-DD');
          console.log('日期处理:', {
            原始: values.date_range,
            开始日期: data.start_date,
            结束日期: data.end_date
          });
        } else {
          data.start_date = null;
          data.end_date = null;
          console.log('日期为空，设置为null');
        }
        delete data.date_range;
        
        // 将status字段映射为test_status
        if (data.status) {
          data.test_status = data.status;
          delete data.status;
          console.log('状态字段映射:', data.test_status);
        }
        
        // 将优先级从显示值映射为后端值
        if (data.priority) {
          const originalPriority = data.priority;
          data.priority = priorityMap[data.priority] || data.priority;
          console.log('优先级映射:', {
            原始显示值: originalPriority,
            映射后端值: data.priority,
            映射表: priorityMap
          });
        }
        
        console.log('最终提交数据:', data);
        onOk(data);
      })
      .catch(info => {
        console.log('表单验证失败:', info);
      });
  };

  const priorityMap = {
    'P1-高': 'p1',
    'P2-中': 'p2',
    'P3-低': 'p3'
  };

  const reversePriorityMap = {
    'p1': 'P1-高',
    'p2': 'P2-中',
    'p3': 'P3-低'
  };

  // 添加模型类型反向映射
  const reverseModelTypeMap = {
    '推理': 'inference',
    '训练': 'training',
    '微调': 'fine_tuning',
    '预训练': 'pre-training',
    '优化': 'optimization'
  };

  // 添加GPU型号反向映射（从display值映射到实际值）
  const reverseGpuModelMap = {
    'P800': 'P800',
    'P800-PCIe': 'P800-PCIe',
    'RG800': 'RG800',
    'MLU370-X8': 'MLU370-X8',
    'K100-AI': 'K100-AI',
    'S60 G7': 'S60',
    'L20': 'L20',
    'C500': 'C500',
    'C550': 'C550',
    'C500X': 'C500X',
    'MR-V100': 'MR-V100',
    'BI-V150': 'BI-V150',
    'GPU-104P': 'GPU-104P'
  };

  // 获取GPU列表和测试用例集列表
  useEffect(() => {
    if (visible) {
      // 获取GPU列表
      http.get('/api/model-storage/gpus/')
        .then(res => {
          const options = res.map(gpu => ({
            value: gpu.name,
            label: gpu.name
          }));
          setGpuOptions(options);
        })
        .catch(err => {
          console.error('获取GPU列表失败:', err);
          // 如果获取失败，使用默认选项
          setGpuOptions([
            { value: 'P800', label: 'P800' },
            { value: 'P800-PCIe', label: 'P800-PCIe' },
            { value: 'RG800', label: 'RG800' },
            { value: 'MLU370-X8', label: 'MLU370-X8' },
            { value: 'K100-AI', label: 'K100-AI' },
            { value: 'S60', label: 'S60 G7' },
            { value: 'L20', label: 'L20' },
            { value: 'C500', label: 'C500' },
            { value: 'C550', label: 'C550' },
            { value: 'C500X', label: 'C500X' },
            { value: 'MR-V100', label: 'MR-V100' },
            { value: 'BI-V150', label: 'BI-V150' },
            { value: 'GPU-104P', label: 'GPU-104P' }
          ]);
        });

      // 获取测试用例集列表
      http.get('/api/exec/test-case-sets/')
        .then(res => {
          const caseSetsData = res.data || res || [];
          const options = caseSetsData.map(caseSet => ({
            value: caseSet.id,
            label: `${caseSet.name}${caseSet.category ? ` (${caseSet.category})` : ''}`,
            description: caseSet.description
          }));
          setTestCaseSetOptions(options);
        })
        .catch(err => {
          console.error('获取测试用例集列表失败:', err);
          setTestCaseSetOptions([]);
        });
    }
  }, [visible]);

  // 添加调试日志和表单值设置
  useEffect(() => {
    console.log('=== TestTaskForm useEffect 触发 ===');
    console.log('visible:', visible);
    console.log('initialValues:', initialValues);
    
    if (initialValues && visible) {
      console.log('开始处理字段映射...');
      
      // 逐个字段映射并记录
      const priorityMapped = reversePriorityMap[initialValues.priority] || initialValues.priority_display || 'P2-中';
      console.log('优先级映射:', {
        原始值: initialValues.priority,
        显示值: initialValues.priority_display,
        映射结果: priorityMapped
      });
      
      const statusMapped = initialValues.test_status;
      console.log('状态映射:', {
        原始值: initialValues.test_status,
        显示值: initialValues.test_status_display,
        映射结果: statusMapped
      });
      
      const dateRange = (initialValues.start_date && initialValues.end_date) ? [moment(initialValues.start_date), moment(initialValues.end_date)] : undefined;
      console.log('日期范围映射:', {
        开始日期: initialValues.start_date,
        结束日期: initialValues.end_date,
        映射结果: dateRange
      });
      
      // 修复模型类型映射逻辑
      const modelTypeMapped = reverseModelTypeMap[initialValues.model_type_display] || initialValues.model_type;
      console.log('模型类型映射:', {
        原始值: initialValues.model_type,
        显示值: initialValues.model_type_display,
        映射结果: modelTypeMapped
      });
      
      // 修复GPU型号映射逻辑
      const gpuModelMapped = reverseGpuModelMap[initialValues.gpu_model_display] || initialValues.gpu_model;
      console.log('GPU型号映射:', {
        原始值: initialValues.gpu_model,
        显示值: initialValues.gpu_model_display,
        映射结果: gpuModelMapped
      });
      
      console.log('资料输出原始值:', initialValues.document_output);
      
      const mappedValues = {
        ...initialValues,
        // 优先级映射：从后端的p1/p2/p3映射到前端显示的P1-高/P2-中/P3-低
        priority: priorityMapped,
        // 状态映射：test_status -> status
        status: statusMapped,
        // 日期范围映射
        date_range: dateRange,
        // 资料输出字段映射
        document_output: initialValues.document_output || false,
        // 修复模型类型和GPU型号映射
        model_type: modelTypeMapped,
        gpu_model: gpuModelMapped,
        progress: initialValues.progress || 0,
        // 测试用例集关联
        test_case_set_id: initialValues.test_case_set_id
      };
      
      console.log('最终映射数据:', mappedValues);
      
      // 延迟设置表单值，确保表单已渲染
      setTimeout(() => {
        console.log('开始设置表单值...');
        form.setFieldsValue(mappedValues);
        
        // 设置后立即获取表单当前值进行验证
        const currentValues = form.getFieldsValue();
        console.log('表单设置后的当前值:', currentValues);
        
        // 检查每个关键字段是否设置成功
        console.log('=== 字段设置验证 ===');
        console.log('模型类型设置结果:', currentValues.model_type, '期望:', mappedValues.model_type);
        console.log('GPU型号设置结果:', currentValues.gpu_model, '期望:', mappedValues.gpu_model);
        console.log('优先级设置结果:', currentValues.priority, '期望:', mappedValues.priority);
        console.log('资料输出设置结果:', currentValues.document_output, '期望:', mappedValues.document_output);
        console.log('状态设置结果:', currentValues.status, '期望:', mappedValues.status);
        
        // 强制触发表单重新渲染
        form.validateFields([]);
      }, 200); // 增加延迟时间
    } else {
      console.log('跳过映射 - visible:', visible, 'initialValues:', !!initialValues);
    }
  }, [initialValues?.id, visible, form]);

  return (
    <Modal
      title={initialValues ? '编辑测试任务' : '新增测试任务'}
      visible={visible}
      onOk={handleOk}
      onCancel={onCancel}
      destroyOnClose
      maskClosable={false}
    >
      <Form 
        form={form} 
        initialValues={initialValues} 
        key={initialValues?.id || 'new'} 
        labelCol={{ span: 6 }} 
        wrapperCol={{ span: 16 }}
      >
        <Form.Item name="model_name" label="模型名称" rules={[{ required: true, message: '请输入模型名称' }]}>
          <Input placeholder="例如：Mixtral-8x7B" />
        </Form.Item>
        <Form.Item name="tester" label="人员名称" rules={[{ required: true, message: '请选择或输入人员名称' }]}>
          <UserSelector placeholder="请输入人员名称，支持模糊搜索" />
        </Form.Item>
        <Form.Item name="model_type" label="模型类型">
          <Select placeholder="请选择模型类型" allowClear>
            <Option value="inference">推理</Option>
            <Option value="training">训练</Option>
            <Option value="fine_tuning">微调</Option>
            <Option value="pre-training">预训练</Option>
            <Option value="optimization">优化</Option>
          </Select>
        </Form.Item>
        <Form.Item name="gpu_model" label="GPU型号">
          <Select placeholder="请选择GPU型号" allowClear>
            {gpuOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item name="test_case_set_id" label="关联用例集">
          <Select placeholder="请选择测试用例集" allowClear>
            {testCaseSetOptions.map(option => (
              <Option key={option.value} value={option.value} title={option.description}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item name="date_range" label="起止日期">
          <RangePicker />
        </Form.Item>
        <Form.Item name="priority" label="优先级">
          <Select placeholder="请选择优先级" allowClear>
            <Option value="P1-高">P1-高</Option>
            <Option value="P2-中">P2-中</Option>
            <Option value="P3-低">P3-低</Option>
          </Select>
        </Form.Item>
        <Form.Item name="progress" label="任务进度">
          <InputNumber min={0} max={100} formatter={value => `${value}%`} parser={value => value.replace('%', '')} style={{ width: '100%' }}/>
        </Form.Item>
        <Form.Item name="status" label="任务状态">
          <Select placeholder="请选择任务状态" allowClear>
            <Option value="pending">待开始</Option>
            <Option value="in_progress">进行中</Option>
            <Option value="completed">已完成</Option>
            <Option value="cancelled">已取消</Option>
            <Option value="blocked">阻塞中</Option>
            <Option value="delayed">已延期</Option>
          </Select>
        </Form.Item>
        <Form.Item name="document_output" label="资料输出" valuePropName="checked">
          <Switch
            checkedChildren="是"
            unCheckedChildren="否"
          />
        </Form.Item>
        <Form.Item name="notes" label="备注">
          <TextArea rows={3} placeholder="请输入备注信息" />
        </Form.Item>
      </Form>
    </Modal>
  );
} 