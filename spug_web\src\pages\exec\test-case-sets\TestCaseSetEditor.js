/**
 * 测试用例集编辑器
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Tabs,
  Table,
  Space,
  Popconfirm,
  message,
  Breadcrumb,
  Alert,
  Row,
  Col,
  Typography,
  Switch,
  Tooltip
} from 'antd';
import {
  SaveOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  FileTextOutlined,
  HolderOutlined,
  EditOutlined
} from '@ant-design/icons';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { http, history } from 'libs';
import styles from './TestCaseSetEditor.module.css';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 可编辑单元格组件
const EditableCell = React.memo(({ value, onChange, placeholder, type = 'input', rows = 1 }) => {
  const [editing, setEditing] = useState(false);
  const [inputValue, setInputValue] = useState(value || '');
  const inputRef = useRef(null);

  useEffect(() => {
    setInputValue(value || '');
  }, [value]);

  useEffect(() => {
    if (editing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [editing]);

  const handleSave = useCallback(() => {
    setEditing(false);
    if (inputValue !== value) {
      onChange(inputValue);
    }
  }, [inputValue, value, onChange]);

  const handleCancel = useCallback(() => {
    setEditing(false);
    setInputValue(value || '');
  }, [value]);

  const handleKeyPress = useCallback((e) => {
    if (e.key === 'Enter' && !e.shiftKey && type === 'input') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  }, [handleSave, handleCancel, type]);

  if (editing) {
    const InputComponent = type === 'textarea' ? Input.TextArea : Input;
    return (
      <InputComponent
        ref={inputRef}
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onBlur={handleSave}
        onKeyDown={handleKeyPress}
        placeholder={placeholder}
        rows={type === 'textarea' ? rows : undefined}
        style={{ margin: '-5px -11px' }}
        autoSize={type === 'textarea' ? { minRows: rows, maxRows: 6 } : undefined}
      />
    );
  }

  return (
    <div
      onClick={() => setEditing(true)}
      style={{
        minHeight: type === 'textarea' ? `${rows * 22 + 10}px` : '32px',
        padding: '4px 8px',
        cursor: 'text',
        border: '1px solid transparent',
        borderRadius: '4px',
        whiteSpace: type === 'textarea' ? 'pre-wrap' : 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        lineHeight: '1.5',
        backgroundColor: 'transparent',
        transition: 'all 0.2s'
      }}
      onMouseEnter={(e) => {
        e.target.style.backgroundColor = '#f5f5f5';
        e.target.style.border = '1px solid #d9d9d9';
      }}
      onMouseLeave={(e) => {
        e.target.style.backgroundColor = 'transparent';
        e.target.style.border = '1px solid transparent';
      }}
      title={value || placeholder}
    >
      {value || <span style={{ color: '#bfbfbf' }}>{placeholder}</span>}
    </div>
  );
});

// 拖拽手柄组件
const DragHandle = SortableHandle(() => (
  <HolderOutlined className={styles.dragHandle} />
));

// 可排序的表格行
const SortableItem = SortableElement(props => <tr {...props} />);

// 可排序的表格容器
const SortableBody = SortableContainer(props => <tbody {...props} />);

function TestCaseSetEditor({ match }) {
  const [testCaseSet, setTestCaseSet] = useState(null);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [testCases, setTestCases] = useState([]);
  const [activeTab, setActiveTab] = useState('basic');
  const isMountedRef = useRef(true);

  const caseSetId = match.params.id;
  const isEdit = caseSetId && caseSetId !== 'new';

  useEffect(() => {
    isMountedRef.current = true;
    
    if (isEdit) {
      fetchTestCaseSet();
    } else {
      // 新建模式，初始化默认数据
      const defaultCases = [
        {
          id: Date.now(),
          name: '测试用例1',
          precondition: '系统正常运行',
          test_steps: '1. 打开应用\n2. 执行操作\n3. 验证结果',
          expected_result: '操作成功，结果符合预期',
          enabled: true,
          sort_order: 1
        }
      ];
      
      const newCaseSet = {
        name: '',
        description: '',
        category: '',
        test_cases: defaultCases
      };
      setTestCaseSet(newCaseSet);
      setTestCases(defaultCases);
      form.setFieldsValue({
        name: newCaseSet.name,
        description: newCaseSet.description,
        category: newCaseSet.category
      });
    }

    return () => {
      isMountedRef.current = false;
    };
  }, [caseSetId]);

  const fetchTestCaseSet = async () => {
    if (!isMountedRef.current) return;
    setLoading(true);
    try {
      const response = await http.get(`/api/exec/test-case-sets/${caseSetId}/`);
      const caseSet = response.data || response;
      console.log('加载的测试用例集数据:', caseSet);
      
      if (!isMountedRef.current) return;
      setTestCaseSet(caseSet);
      
      const cases = caseSet.test_cases || [];
      setTestCases(cases);
      
      form.setFieldsValue({
        name: caseSet.name,
        description: caseSet.description,
        category: caseSet.category
      });
    } catch (error) {
      if (isMountedRef.current) {
        message.error('加载测试用例集失败');
      }
      console.error('Fetch test case set error:', error);
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      const caseSetData = {
        ...values,
        test_cases: testCases
      };

      console.log('保存的测试用例集数据:', caseSetData);

      if (isEdit) {
        await http.put(`/api/exec/test-case-sets/${caseSetId}/`, caseSetData);
        message.success('测试用例集更新成功');
      } else {
        await http.post('/api/exec/test-case-sets/', caseSetData);
        message.success('测试用例集创建成功');
      }
      
      handleBack();
    } catch (error) {
      message.error('保存失败');
      console.error('Save test case set error:', error);
    }
  };

  const handleBack = () => {
    history.push('/exec/test-case-sets');
  };

  const handleAddTestCase = () => {
    const newCase = {
      id: Date.now(),
      name: `测试用例${testCases.length + 1}`,
      precondition: '',
      test_steps: '',
      expected_result: '',
      enabled: true,
      sort_order: testCases.length + 1
    };
    setTestCases([...testCases, newCase]);
  };

  const handleDeleteTestCase = (caseId) => {
    setTestCases(testCases.filter(c => c.id !== caseId));
  };

  const handleTestCaseChange = useCallback((caseId, field, value) => {
    setTestCases(prevCases => prevCases.map(c =>
      c.id === caseId ? { ...c, [field]: value } : c
    ));
  }, []);

  const handleSortEnd = ({ oldIndex, newIndex }) => {
    if (oldIndex === newIndex) return;
    
    const newCases = [...testCases];
    const [removed] = newCases.splice(oldIndex, 1);
    newCases.splice(newIndex, 0, removed);
    
    // 更新排序
    const updatedCases = newCases.map((c, index) => ({
      ...c,
      sort_order: index + 1
    }));
    
    setTestCases(updatedCases);
  };

  const columns = [
    {
      title: '排序',
      dataIndex: 'sort',
      width: 60,
      render: () => <DragHandle />,
    },
    {
      title: '用例名称',
      dataIndex: 'name',
      width: 200,
      render: (text, record) => (
        <EditableCell
          value={text}
          onChange={(value) => handleTestCaseChange(record.id, 'name', value)}
          placeholder="请输入用例名称"
          type="input"
        />
      ),
    },
    {
      title: '预置条件',
      dataIndex: 'precondition',
      width: 250,
      render: (text, record) => (
        <EditableCell
          value={text}
          onChange={(value) => handleTestCaseChange(record.id, 'precondition', value)}
          placeholder="请输入预置条件"
          type="textarea"
          rows={2}
        />
      ),
    },
    {
      title: '测试步骤',
      dataIndex: 'test_steps',
      width: 300,
      render: (text, record) => (
        <EditableCell
          value={text}
          onChange={(value) => handleTestCaseChange(record.id, 'test_steps', value)}
          placeholder="请输入测试步骤"
          type="textarea"
          rows={3}
        />
      ),
    },
    {
      title: '预期结果',
      dataIndex: 'expected_result',
      width: 250,
      render: (text, record) => (
        <EditableCell
          value={text}
          onChange={(value) => handleTestCaseChange(record.id, 'expected_result', value)}
          placeholder="请输入预期结果"
          type="textarea"
          rows={2}
        />
      ),
    },
    {
      title: '启用',
      dataIndex: 'enabled',
      width: 80,
      render: (enabled, record) => (
        <Switch
          checked={enabled}
          onChange={(checked) => handleTestCaseChange(record.id, 'enabled', checked)}
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space>
          <Popconfirm
            title="确定要删除这个测试用例吗？"
            onConfirm={() => handleDeleteTestCase(record.id)}
            okText="删除"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              size="small"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const SortableTable = (props) => {
    const { children, ...restProps } = props;
    return <SortableBody {...restProps} onSortEnd={handleSortEnd} useDragHandle>{children}</SortableBody>;
  };

  const SortableRow = (props) => {
    const { children, ...restProps } = props;
    const index = testCases.findIndex(x => x.id === restProps['data-row-key']);
    return <SortableItem index={index} {...restProps}>{children}</SortableItem>;
  };

  if (loading) {
    return <div>加载中...</div>;
  }

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: 'calc(100vh - 64px)' }}>
      {/* 面包屑导航 */}
      <Breadcrumb style={{ marginBottom: '16px' }}>
        <Breadcrumb.Item>脚本配置中心</Breadcrumb.Item>
        <Breadcrumb.Item>
          <a onClick={handleBack} style={{ cursor: 'pointer' }}>测试用例集</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{isEdit ? '编辑' : '新建'}</Breadcrumb.Item>
      </Breadcrumb>

      {/* 标题和操作按钮 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '24px',
        background: 'white',
        padding: '16px 24px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Title level={3} style={{ margin: 0 }}>
          {isEdit ? `编辑测试用例集: ${testCaseSet?.name || ''}` : '新建测试用例集'}
        </Title>
        <Space>
          <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
            返回
          </Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
            保存用例集
          </Button>
        </Space>
      </div>

      {/* 主要内容 */}
      <Card style={{ borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<span><InfoCircleOutlined />基本信息</span>} key="basic">
            <Row gutter={24}>
              <Col span={12}>
                <Form form={form} layout="vertical">
                  <Form.Item
                    name="name"
                    label="测试用例集名称"
                    rules={[{ required: true, message: '请输入测试用例集名称' }]}
                  >
                    <Input placeholder="例如: 用户登录功能测试用例集" />
                  </Form.Item>
                  
                  <Form.Item
                    name="category"
                    label="分类"
                  >
                    <Input placeholder="例如: functional, performance, security" />
                  </Form.Item>
                  
                  <Form.Item
                    name="description"
                    label="描述"
                  >
                    <Input.TextArea 
                      rows={4} 
                      placeholder="描述这个测试用例集的用途和范围..."
                    />
                  </Form.Item>
                </Form>
              </Col>
              <Col span={12}>
                <Alert
                  message="测试用例集说明"
                  description={
                    <div>
                      <p><strong>用例集结构：</strong></p>
                      <ul>
                        <li>📝 预置条件：执行测试前需要满足的条件</li>
                        <li>🔄 测试步骤：详细的操作步骤</li>
                        <li>✅ 预期结果：期望的测试结果</li>
                      </ul>
                      <p><strong>统计信息：</strong></p>
                      <ul>
                        <li>📋 测试用例: {testCases.length} 个 (启用: {testCases.filter(c => c.enabled).length} 个)</li>
                      </ul>
                    </div>
                  }
                  type="info"
                  showIcon
                />
              </Col>
            </Row>
          </TabPane>

          <TabPane tab={<span><FileTextOutlined />测试用例 ({testCases.length})</span>} key="cases">
            <div style={{ marginBottom: '16px' }}>
              <Space>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />} 
                  onClick={handleAddTestCase}
                >
                  📝 添加用例
                </Button>
                <Text type="secondary">
                  共 {testCases.length} 个用例，启用 {testCases.filter(c => c.enabled).length} 个
                </Text>
              </Space>
              <Alert
                message="用例说明"
                description={
                  <div>
                    <p>每个测试用例包含预置条件、测试步骤和预期结果三个部分。</p>
                    <p><strong>🔄 拖拽排序：</strong>点击并拖拽 <HolderOutlined style={{ color: '#1890ff' }} /> 图标可以调整用例执行顺序</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginTop: '16px' }}
              />
            </div>

            <Table
              dataSource={testCases}
              columns={columns}
              rowKey="id"
              pagination={false}
              scroll={{ x: 1200 }}
              components={{
                body: {
                  wrapper: SortableTable,
                  row: SortableRow,
                },
              }}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
}

export default TestCaseSetEditor;
