import React from 'react';
import { observer } from 'mobx-react';
import { Row, Col, PageHeader, Breadcrumb } from 'antd';
import { DatabaseOutlined, HomeOutlined } from '@ant-design/icons';
import ServerMetrics from './ServerMetrics';
import TestPlanStatistics from './TestPlanStatistics';
import ReleasePlanCard from './ReleasePlanCard';
import styles from './index.module.css';

export default observer(function ModelStorage() {
  return (
    <div className={styles.container}>
      {/* 页面头部 */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '16px',
        padding: '20px 24px',
        marginBottom: '24px',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <Breadcrumb style={{ marginBottom: '16px' }}>
          <Breadcrumb.Item>
            <HomeOutlined style={{ color: 'rgba(255, 255, 255, 0.8)' }} />
            <span style={{ marginLeft: '8px', color: 'rgba(255, 255, 255, 0.8)' }}>首页</span>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <DatabaseOutlined style={{ color: 'white' }} />
            <span style={{ marginLeft: '8px', color: 'white' }}>模型存储</span>
          </Breadcrumb.Item>
        </Breadcrumb>
        
        <PageHeader
          ghost={false}
          title={
            <div style={{ 
              display: 'flex', 
              alignItems: 'center',
              fontSize: '28px',
              fontWeight: 'bold',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              <DatabaseOutlined style={{ marginRight: '12px', color: '#667eea' }} />
              模型存储监控
            </div>
          }
          subTitle={
            <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
              实时监控服务器状态、文件管理和发布计划
            </span>
          }
          style={{ padding: 0 }}
        />
      </div>

      {/* 主要内容区域 */}
      <div style={{ 
        background: 'rgba(255, 255, 255, 0.05)',
        borderRadius: '16px',
        padding: '24px',
        backdropFilter: 'blur(10px)'
      }}>
        {/* 服务器监控 - 缩小版本 */}
        <Row gutter={[24, 24]}>
          <Col span={24}>
            <div style={{ height: '120px', overflow: 'hidden' }}>
              <ServerMetrics compact={true} />
            </div>
          </Col>
        </Row>

        {/* 测试计划统计 - 横向布局 */}
        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col span={24}>
            <TestPlanStatistics />
          </Col>
        </Row>
      </div>
    </div>
  );
});