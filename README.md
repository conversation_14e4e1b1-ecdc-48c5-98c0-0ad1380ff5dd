# Spug 项目

一个基于 Django + React 的运维管理平台，支持模型存储、GPU管理、测试计划等功能。

## 📁 项目结构

```
spug/
├── spug_api/           # 后端API服务 (Django)
├── spug_web/           # 前端界面 (React)
├── docs/               # 📚 文档目录
│   ├── api/           # API接口文档
│   ├── user-manual/   # 用户使用手册
│   └── development/   # 开发相关文档
├── scripts/           # 🔧 脚本文件
├── config/            # ⚙️ 配置文件
├── assets/            # 🖼️ 图片和静态资源
├── temp/              # 🗂️ 临时文件和测试文件
└── logs/              # 📝 日志文件
```

## 🚀 快速开始

### 启动服务
```bash
# 使用启动脚本
.\scripts\start.ps1
```

### 手动启动

#### 后端服务
```bash
cd spug_api
python manage.py runserver
```

#### 前端服务
```bash
cd spug_web
npm start
```

## 📚 文档说明

### API文档
- `docs/api/API_DOCUMENTATION.md` - 完整API接口文档
- `docs/api/发布计划API对接文档.md` - 发布计划相关API

### 用户手册
- `docs/user-manual/model-storage-user-manual.html` - 模型存储功能使用手册
- `docs/user-manual/test-plan-user-manual.html` - 测试计划功能使用手册
- `docs/user-manual/文件详细对比功能说明.md` - 文件对比功能说明

### 开发文档
- `docs/development/BACKEND_LIBRARIES.md` - 后端依赖库说明
- `docs/development/COMPONENT_REFERENCE.md` - 前端组件参考
- `docs/development/QUICK_START_GUIDE.md` - 快速开发指南
- `docs/development/README_DOCUMENTATION.md` - 项目说明文档
- `docs/development/项目结构.md` - 详细项目结构说明
- `docs/development/Tread.md` - 线程相关文档

## ⚙️ 配置文件

- `config/funboost_config.py` - Funboost配置
- `config/nb_log_config.py` - 日志配置
- `config/funboost_cli_user.py` - Funboost CLI用户配置

## 🖼️ 资源文件

`assets/` 目录包含项目相关的截图和图片资源：
- 任务执行.png
- 引入命令.png
- 执行记录.png
- 测试计划相关截图等

## 🗂️ 临时文件

`temp/` 目录包含测试文件和临时文件：
- `quick_test.py` - 快速测试脚本
- `导入模板测试.xlsx` - 导入功能测试文件

## 📝 日志

项目运行日志存储在 `logs/` 目录中，按日期和模块分类。

## 🔧 维护

- 定期清理 `temp/` 目录中的临时文件
- 日志文件会自动轮转，旧日志可定期清理
- 配置文件修改后需要重启相应服务

## 📄 许可证

详见 `LICENSE` 文件。
