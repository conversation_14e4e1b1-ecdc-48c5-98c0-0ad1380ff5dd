import React, { useState, useEffect } from 'react';
import { Card, Button, Table, Tag, message, Space } from 'antd';
import { PlusOutlined, EyeOutlined, BarChartOutlined, FileExcelOutlined } from '@ant-design/icons';
import { http } from 'libs';
import moment from 'moment';

export default function ReleasePlanCard() {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = () => {
    setLoading(true);
    http.get('/api/model-storage/new-release-plans/')
      .then(res => {
        // 只显示最新的5条记录
        const plansData = res.slice(0, 5).map(plan => ({
          ...plan,
          timeRange: [moment(plan.start_date), moment(plan.end_date)]
        }));
        setPlans(plansData);
      })
      .catch(err => {
        message.error('获取发布计划失败：' + err.message);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 新增：周报导出功能
  const handleExportWeeklyReport = () => {
    message.info('请进入具体的发布计划页面来导出该计划的周报');
  };

  const columns = [
    {
      title: '计划名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status_display',
      key: 'status',
      render: status => {
        let color = 'geekblue';
        if (status === '已完成') {
          color = 'green';
        } else if (status === '进行中') {
          color = 'volcano';
        } else if (status === '计划中') {
          color = 'blue';
        } else if (status === '已取消') {
          color = 'default';
        }
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: '起止时间',
      key: 'timeRange',
      render: (_, record) => `${moment(record.start_date).format('MM-DD')} ~ ${moment(record.end_date).format('MM-DD')}`,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button 
          type="link" 
          size="small"
          icon={<BarChartOutlined />}
          onClick={() => window.open(`/model-storage/release-plan-gantt/${record.id}`, '_blank')}
        >
          甘特图
        </Button>
      ),
    },
  ];

  const handleViewAll = () => {
    window.open('/model-storage/release-plan', '_blank');
  };

  const handleCreateNew = () => {
    window.open('/model-storage/release-plan', '_blank');
  };

  return (
    <Card 
      title="发布计划"
      extra={
        <Space>
          <Button 
            type="default" 
            size="small"
            icon={<FileExcelOutlined />} 
            onClick={handleExportWeeklyReport}
            loading={exportLoading}
            style={{ 
              color: '#52c41a',
              borderColor: '#52c41a'
            }}
          >
            导出周报
          </Button>
          <Button 
            type="primary" 
            size="small"
            icon={<PlusOutlined />} 
            onClick={handleCreateNew}
          >
            新建
          </Button>
          <Button 
            size="small"
            icon={<EyeOutlined />} 
            onClick={handleViewAll}
          >
            查看全部
          </Button>
        </Space>
      }
    >
      <Table 
        columns={columns} 
        dataSource={plans} 
        rowKey="id" 
        loading={loading}
        pagination={false}
        size="small"
        showHeader={plans.length > 0}
        locale={{
          emptyText: '暂无发布计划'
        }}
      />
    </Card>
  );
} 