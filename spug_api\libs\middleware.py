# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from .utils import json_response, get_request_real_ip
from apps.account.models import User
from apps.setting.utils import AppSetting
import traceback
import time


class HandleExceptionMiddleware(MiddlewareMixin):
    """
    处理试图函数异常
    """

    def process_exception(self, request, exception):
        traceback.print_exc()
        return json_response(error='Exception: %s' % exception)


class CORSMiddleware(MiddlewareMixin):
    """
    处理跨域请求
    """
    def process_response(self, request, response):
        # 添加CORS头
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept, X-Token, Authorization'
        response['Access-Control-Max-Age'] = '86400'  # 24小时
        
        # 处理预检请求
        if request.method == 'OPTIONS':
            response.status_code = 200
            return response
            
        return response


class AuthenticationMiddleware(MiddlewareMixin):
    """
    登录验证
    """

    def process_request(self, request):
        # 添加调试信息
        print(f"[MIDDLEWARE DEBUG] 请求路径: {request.path}")
        print(f"[MIDDLEWARE DEBUG] 请求方法: {request.method}")
        
        # 优先检查认证排除列表
        if request.path in settings.AUTHENTICATION_EXCLUDES:
            print(f"[MIDDLEWARE DEBUG] 路径在排除列表中: {request.path}")
            return None
        if any(x.match(request.path) for x in settings.AUTHENTICATION_EXCLUDES if hasattr(x, 'match')):
            print(f"[MIDDLEWARE DEBUG] 路径匹配排除正则: {request.path}")
            return None
        
        # 内部系统 - 跳过model-storage相关的认证
        if (request.path.startswith('/model-storage/') or 
            request.path.startswith('/api/model-storage/') or
            '/model-storage/' in request.path):
            print(f"[MIDDLEWARE DEBUG] 跳过认证: {request.path}")
            return None
            
        print(f"[MIDDLEWARE DEBUG] 需要认证，检查token...")
        access_token = request.headers.get('x-token') or request.GET.get('x-token')
        if access_token and len(access_token) == 32:
            x_real_ip = get_request_real_ip(request.headers)
            user = User.objects.filter(access_token=access_token).first()
            if user and user.token_expired >= time.time() and user.is_active:
                if x_real_ip == user.last_ip or AppSetting.get_default('bind_ip') is False:
                    request.user = user
                    user.token_expired = time.time() + settings.TOKEN_TTL
                    user.save()
                    print(f"[MIDDLEWARE DEBUG] 认证成功: {user.username}")
                    return None
        
        print(f"[MIDDLEWARE DEBUG] 认证失败，返回401")
        response = json_response(error="验证失败，请重新登录")
        response.status_code = 401
        return response
