import os
import sys
import subprocess
import time
import re
import argparse
import csv
from datetime import datetime
from typing import List, Tuple, Dict, Any

# 默认配置参数
DEFAULT_BACKEND = "vllm"
DEFAULT_HOST = "0.0.0.0"
DEFAULT_PORT = 8000
DEFAULT_MODEL_PATH = "/home/<USER>/Qwen2-72B"
DEFAULT_SERVICE_NAME = "Qwen2-72B"
DEFAULT_DATASET_PATH = "/root/ShareGPT_V3_unfiltered_cleaned_split.json"
DEFAULT_IO_COMBINATIONS: List[Tuple[int, int]] = [
    (128, 128),
    (256, 256),
    (128, 2048),
    (2048, 128),
    (1024, 1024),
    (2048, 2048)
]

# 日志配置
LOG_DIR = "benchmark_{backend}_logs"
TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")
MAIN_LOG = os.path.join(LOG_DIR, f"full_test_{TIMESTAMP}.log")

class DualLogger:
    """同时输出到文件和终端的日志记录器"""
    def __init__(self, filename):
        self.console = sys.stdout
        self.file = open(filename, "a")

    def write(self, message):
        self.console.write(message)
        self.file.write(message)

    def flush(self):
        self.console.flush()
        self.file.flush()

def setup_logging():
    """初始化日志系统"""
    os.makedirs(LOG_DIR, exist_ok=True)
    sys.stdout = DualLogger(MAIN_LOG)
    sys.stderr = sys.stdout

def parse_combinations(combination_str: str) -> List[Tuple[int, int]]:
    """解析输入输出组合字符串"""
    combinations = []
    parts = combination_str.split(';')
    for part in parts:
        part = part.strip()
        if not part:
            continue
        if ':' not in part:
            raise ValueError(f"Invalid combination format in '{part}'. Expected 'input:output'.")
        input_str, output_str = part.split(':', 1)
        try:
            input_len = int(input_str)
            output_len = int(output_str)
        except ValueError:
            raise ValueError(f"Invalid integer values in combination '{part}'. Input and output must be integers.")
        combinations.append((input_len, output_len))
    if not combinations:
        raise ValueError("No valid combinations provided.")
    return combinations

def parse_log(log_file: str) -> Dict[str, Any]:
    """从日志文件提取性能指标"""
    metrics = {
        'total_time': None,
        'req_throughput': None,
        'output_throughput': None,
        'total_throughput': None,
        'mean_ttft': None,
        'mean_tpot': None
    }
    
    try:
        with open(log_file, 'r') as f:
            content = f.read()

        # 提取总耗时
        time_match = re.search(r'Benchmark\s+duration\s+\(s\):\s+([\d.]+)', content)
        if time_match:
            metrics['total_time'] = float(time_match.group(1))

        # 提取请求吞吐量
        req_match = re.search(r'Request\s+throughput\s+\(req/s\):\s+([\d.]+)', content)
        if req_match:
            metrics['req_throughput'] = float(req_match.group(1))

        # 提取输出token吞吐量
        output_match = re.search(r'Output\s+token\s+throughput\s+\(tok/s\):\s+([\d.]+)', content)
        if output_match:
            metrics['output_throughput'] = float(output_match.group(1))

        # 提取总token吞吐量
        total_match = re.search(r'Total\s+token\s+throughput\s+\(tok/s\):\s+([\d.]+)', content)
        if total_match:
            metrics['total_throughput'] = float(total_match.group(1))

        # 提取TTFT和TPOT
        ttft_match = re.search(r'Mean\s+TTFT\s+\(ms\):\s+([\d.]+)', content)
        tpot_match = re.search(r'Mean\s+TPOT\s+\(ms\):\s+([\d.]+)', content)
        
        if ttft_match:
            metrics['mean_ttft'] = float(ttft_match.group(1))
        if tpot_match:
            metrics['mean_tpot'] = float(tpot_match.group(1))

        # 调试缺失字段
        missing = [k for k, v in metrics.items() if v is None]
        if missing:
            print(f"[ERROR] 缺失字段: {', '.join(missing)}")
            print(f"[DEBUG] 当前解析结果:\n{metrics}")
            return None

    except Exception as e:
        print(f"Error parsing {log_file}: {e}")
    
    return metrics

def request_count(concurrency: int) -> int:
    """计算并发请求数"""
    if 1 <= concurrency <= 4:
        requests = concurrency * 8
    elif concurrency <= 256:
        requests = concurrency * 4
    else:
        requests = concurrency
    return requests

def run_benchmark(concurrency: int, input_len: int, output_len: int, 
                  host: str, port: int, model_path: str, service_name: str,backend: str, dataset_path: str, ttft_strandard: float, tpot_strandard: float) -> Tuple[bool, str]:
    """执行单个基准测试"""
    requests = request_count(concurrency)
    
    log_file = os.path.join(LOG_DIR, f"concurrency_{concurrency}_requests_{requests}_input_{input_len}_output_{output_len}.log")
    
    print("\n" + "=" * 70)
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] START: concurrency={concurrency} requests={requests} input={input_len} output={output_len}")
    print(f"[*]    Search for standards in this test:MEAN TTFT {ttft_strandard},MEAN TPOT {tpot_strandard}    [*]")

    try:
        with open(log_file, "w") as f:
            if backend == "vllm":
                cmd = [
                    "python3", "benchmark_serving.py",
                    "--host", host,
                    "--port", str(port),
                    "--backend", backend,
                    "--model", model_path,
                    "--served-model-name", service_name,
                    "--dataset-name", "random",
                    "--num-prompts", str(requests),
                    "--max-concurrency", str(concurrency),
                    "--random-input-len", str(input_len),
                    "--random-output-len", str(output_len),
                    "--ignore-eos"
                ]
            else:
                cmd = [
                    "python3", "-m", "sglang.bench_serving",
                    "--host", host,
                    "--port", str(port),
                    "--backend", backend,
                    "--model", model_path,
                    "--dataset-name", "random",
                    "--num-prompts", str(requests),
                    "--max-concurrency", str(concurrency),
                    "--random-input-len", str(input_len),
                    "--random-output-len", str(output_len),
                    "--dataset-path", dataset_path
                ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True
            )

            # 实时输出日志
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())
                    f.write(output)

            exit_code = process.poll()
            success = exit_code == 0
            status = "SUCCESS" if success else "FAILED"
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {status}: c={concurrency} r={requests} i={input_len} o={output_len}")
            return success, log_file

    except Exception as e:
        print(f"[!] Exception occurred: {str(e)}")
        return False, log_file
    finally:
        print("=" * 70 + "\n")
        time.sleep(15)  # 冷却间隔

def check_oom(log_file: str) -> bool:
    """检查日志中是否包含OOM错误"""
    try:
        with open(log_file, "r") as f:
            return "OutOfMemoryError" in f.read()
    except:
        return False


def write_csv(results: List[Dict], filename: str):
    """将结果写入 CSV 文件"""
    headers = [
        'input_len', 'output_len', 'max_concurrency', 'requests',
        'total_time', 'req_throughput', 'output_throughput',
        'total_throughput', 'mean_ttft', 'mean_tpot', 'output_per_request'
    ]

    # 第一步：计算每列的最大宽度
    column_widths = {header: len(header) for header in headers}  # 初始化为标题长度

    # 遍历所有数据，计算每列最大宽度
    all_rows = []
    for result in results:
        metrics = result.get('metrics', {})
        row = []

        # 提取基础参数
        input_len = str(result['input_len'])
        output_len = str(result['output_len'])
        max_concurrency = str(result['max_concurrency'])
        requests = str(result['requests'])

        # 提取性能指标并格式化
        total_time = f"{metrics.get('total_time', 0):.2f}" if metrics.get('total_time') is not None else 'N/A'
        req_throughput = f"{metrics.get('req_throughput', 0):.2f}" if metrics.get('req_throughput') is not None else 'N/A'
        output_throughput = f"{metrics.get('output_throughput', 0):.2f}" if metrics.get('output_throughput') is not None else 'N/A'
        total_throughput = f"{metrics.get('total_throughput', 0):.2f}" if metrics.get('total_throughput') is not None else 'N/A'
        mean_ttft = f"{metrics.get('mean_ttft', 0):.2f}" if metrics.get('mean_ttft') is not None else 'N/A'
        mean_tpot = f"{metrics.get('mean_tpot', 0):.2f}" if metrics.get('mean_tpot') is not None else 'N/A'

        # 计算 output_per_request
        if req_throughput != 'N/A' and output_throughput != 'N/A' and float(req_throughput) != 0:
            output_per_request = f"{float(output_throughput) / float(req_throughput):.2f}"
        else:
            output_per_request = 'N/A'

        # 构建行数据
        row_data = [
            input_len, output_len, max_concurrency, requests,
            total_time, req_throughput, output_throughput,
            total_throughput, mean_ttft, mean_tpot, output_per_request
        ]
        all_rows.append(row_data)

        # 更新列宽
        for i, value in enumerate(row_data):
            column_widths[headers[i]] = max(column_widths[headers[i]], len(str(value)))

    # 第二步：写入文件（动态对齐）
    with open(filename, 'w') as f:
        # 写入标题行
        header_line = "|".join([header.ljust(column_widths[header]) for header in headers])
        f.write(header_line + "\n")

        # 写入分隔线（可选）
        separator = "|".join(["-" * column_widths[header] for header in headers])
        f.write(separator + "\n")

        # 写入数据行
        for row in all_rows:
            formatted_row = []
            for i, value in enumerate(row):
                # 数值右对齐，字符串左对齐
                if headers[i] in ['input_len', 'output_len', 'max_concurrency', 'requests']:
                    formatted = str(value).rjust(column_widths[headers[i]])
                else:
                    formatted = str(value).ljust(column_widths[headers[i]])
                formatted_row.append(formatted)
            f.write("|".join(formatted_row) + "\n")

def main():
    # 初始化日志系统
    setup_logging()
    print(f"========== DeepSeek-R1 Benchmark Test @ {TIMESTAMP} ==========\n")

    # 处理输入输出组合
    try:
        if args.combination:
            io_combinations = parse_combinations(args.combination)
        else:
            io_combinations = DEFAULT_IO_COMBINATIONS
        print(f"Using IO combinations: {io_combinations}")
    except ValueError as e:
        print(f"Error parsing combinations: {e}")
        sys.exit(1)

    results = []
    current_concurrency = max(args.start_concurrency, 1)

    # 遍历每个IO组合
    for io_idx, (input_len, output_len) in enumerate(io_combinations):
        print(f"\n{'#' * 40} Testing IO Combination {io_idx+1}/{len(io_combinations)}: input={input_len} output={output_len} {'#' * 40}")
        
        max_valid_concurrency = 0
        best_metrics = {}
        current_concurrency = args.start_concurrency
        
        while True:
            success, log_file = run_benchmark(
                current_concurrency, 
                input_len, 
                output_len,
                args.host,
                args.port,
                args.model_path,
                args.model_name,
                args.backend,
                args.dataset_path,
                args.ttft_standard,
                args.tpot_standard
            )
            
            failure_reason = ""
            metrics = {}
            
            # 检查运行状态
            if not success or check_oom(log_file):
                failure_reason = "OOM" if check_oom(log_file) else "Execution failed"
                should_break = True
            else:
                metrics = parse_log(log_file)
                
                # 检查指标有效性
                if all(metrics.values()) == "N/A":
                    failure_reason = "Missing metrics in log"
                    should_break = True
                else:
                    # 检查阈值
                    if metrics['mean_ttft'] > args.ttft_standard or metrics['mean_tpot'] > args.tpot_standard:
                        failure_reason = f"Threshold exceeded (TTFT={metrics['mean_ttft']}ms, TPOT={metrics['mean_tpot']}ms)"
                        should_break = True
                    else:
                        max_valid_concurrency = current_concurrency
                        best_metrics = metrics
                        current_concurrency += 1
                        should_break = False
                print(f"[!] {failure_reason}")

            if should_break:
                if max_valid_concurrency > 0:
                    print(f"✅ IO组合 [input={input_len}, output={output_len}] 最大并发数: {max_valid_concurrency}")
                    results.append({
                        'input_len': input_len,
                        'output_len': output_len,
                        'max_concurrency': max_valid_concurrency,
                        'requests': request_count(max_valid_concurrency),
                        'metrics': best_metrics
                    })
                else:
                    print(f"❌ IO组合 [input={input_len}, output={output_len}] 无有效并发数")
                break

    # 生成最终报告
    csv_filename = os.path.join(LOG_DIR, f"benchmark_summary_{TIMESTAMP}.csv")
    write_csv(results, csv_filename)
    print(subprocess.run(['cat', csv_filename]))

    print("\n" + "=" * 60)
    print(f"测试完成，结果已保存至: {csv_filename}")
    print("=" * 60 + "\n")

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Run DeepSeek-R1 benchmark tests.')
    parser.add_argument('--backend', type=str, default=DEFAULT_BACKEND, help=f'Backend type (default: {DEFAULT_BACKEND})')
    parser.add_argument('--host', type=str, default=DEFAULT_HOST, help=f'Host address (default: {DEFAULT_HOST})')
    parser.add_argument('--port', type=int, default=DEFAULT_PORT, help=f'Port number (default: {DEFAULT_PORT})')
    parser.add_argument('--model-path', type=str, default=DEFAULT_MODEL_PATH,
                        help=f'Path to model directory (default: {DEFAULT_MODEL_PATH})')
    parser.add_argument('--model-name', type=str, default=DEFAULT_SERVICE_NAME,
                        help=f'Service name (default: {DEFAULT_SERVICE_NAME})')
    parser.add_argument('--dataset-path', type=str, default=DEFAULT_DATASET_PATH,
                        help=f'Dataset path (default: {DEFAULT_DATASET_PATH})')
    parser.add_argument('--start-concurrency', type=int, default=1,
                    help='Initial concurrency value to start testing (default: 1)')
    parser.add_argument('--combination', type=str,
                        help='Custom input-output combinations in "input1:output1;input2:output2" format')
    parser.add_argument('--ttft-standard', type=float, default=5.0,
                        help='TTFT threshold in milliseconds (default: 5)')
    parser.add_argument('--tpot-standard', type=float, default=100.0,
                        help='TPOT threshold in milliseconds (default: 100)')
    args = parser.parse_args()
    main()