import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Card, 
  Tree, 
  Spin, 
  Alert, 
  Tag, 
  Input, 
  Space,
  Row,
  Col,
  Typography,
  Breadcrumb,
  Button,
  Empty
} from 'antd';
import { 
  FolderOutlined, 
  GlobalOutlined, 
  HomeOutlined, 
  DiffOutlined, 
  ReloadOutlined, 
  CloudOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { http } from '../../libs';
import styles from './FileDetailCompare.module.less';

const { Search } = Input;
const { Title } = Typography;

export default function FileDetailCompare() {
  const [loading, setLoading] = useState(false);
  const [localTreeData, setLocalTreeData] = useState([]);
  const [remoteTreeData, setRemoteTreeData] = useState([]);
  const [localExpandedKeys, setLocalExpandedKeys] = useState(['local_root']);
  const [remoteExpandedKeys, setRemoteExpandedKeys] = useState(['remote_root']);
  const [searchValue, setSearchValue] = useState('');
  const [error, setError] = useState(null);
  const [diffStatus, setDiffStatus] = useState({
    local: {},
    remote: {}
  });
  
  // 文档统计功能 - 在新标签页打开
  const handleDocStatsModal = () => {
    // 在新标签页中打开文档统计页面
    const url = '/model-storage/doc-statistics';
    window.open(url, '_blank');
  };

  const updateTreeData = (list, key, children) => {
    return list.map((node) => {
      if (node.key === key) {
        return {
          ...node,
          children,
        };
      }
      if (node.children) {
        return {
          ...node,
          children: updateTreeData(node.children, key, children),
        };
      }
      return node;
    });
  };

  const config = useMemo(() => ({
    basePath: new URLSearchParams(window.location.search).get('basePath') || '/HDD_Raid/SVN_MODEL_REPO',
    remoteUrl: new URLSearchParams(window.location.search).get('remoteUrl') || 'http://10.63.30.93/GPU_MODEL_REPO/01.DEV/',
    compareId: new URLSearchParams(window.location.search).get('compareId'),
  }), []);

  const loadDiffInfo = async () => {
    if (!config.compareId) return;
    try {
      const res = await http.get(`/api/model-storage/compare/${config.compareId}/diff-status/`);
      const { local = {}, remote = {} } = res.data || {};
      setDiffStatus({ local, remote });
    } catch (error) {
      console.error('加载差异信息失败:', error);
    }
  };

  const getGitStatusTag = useCallback((status) => {
    if (!status) return null;

    const statusMap = {
      'A': { color: 'success', text: '新增' },
      'M': { color: 'warning', text: '修改' },
      'D': { color: 'error', text: '删除' },
      'R': { color: 'processing', text: '重命名' },
    };

    return statusMap[status] ? (
      <Tag color={statusMap[status].color} style={{ marginLeft: 8 }}>
        {statusMap[status].text}
      </Tag>
    ) : null;
  }, []);

  const buildErrorNode = useCallback((source) => ({
    title: source === 'local' ? '📁 本地仓库 (加载失败)' : '🌐 远程仓库 (加载失败)',
    key: `${source}_root`,
    icon: source === 'local' ? <FolderOutlined /> : <GlobalOutlined />,
    children: [{
      title: '❌ 加载失败，请点击刷新重试',
      key: `${source}_error_msg`,
      isLeaf: true
    }]
  }), []);

  const buildTreeNode = useCallback((item, index, source) => {
    console.log(`🔧 [buildTreeNode] ${source} - 构建节点 ${index}:`, item);
    
    const isFolder = item.data?.type === 'folder';
    const baseTitle = item.data?.name || item.title || `Item_${index}`;
    const statusTag = getGitStatusTag(item.data?.status);
    
    // 处理图标：如果是Unicode编码，进行解码
    let iconDisplay = item.icon;
    if (typeof item.icon === 'string' && item.icon.includes('\\u')) {
      try {
        iconDisplay = JSON.parse(`"${item.icon}"`);
      } catch (e) {
        iconDisplay = item.icon;
      }
    }
    
    const node = {
      title: (
        <span>
          {iconDisplay} {baseTitle}
          {statusTag}
        </span>
      ),
      key: item.key || `${source}_${index}`,
      isLeaf: !isFolder,
      data: {
        ...item.data,
        source: source
      }
    };
    
    console.log(`✅ [buildTreeNode] ${source} - 节点构建完成:`, node);
    return node;
  }, [getGitStatusTag]);

  const buildLocalTree = useCallback((response) => {
    console.log('🏗️ [buildLocalTree] 开始构建本地树...');
    console.log('📦 [buildLocalTree] 原始响应数据:', response);
    console.log('📁 [buildLocalTree] children数据:', response?.children);
    
    const result = [{
      title: '📁 本地仓库',
      key: 'local_root',
      children: (response?.children || [])
        .slice(0, 20)
        .map((item, index) => {
          const node = buildTreeNode(item, index, 'local');
          console.log(`📄 [buildLocalTree] 构建节点 ${index}:`, node);
          return node;
        })
    }];
    
    console.log('✅ [buildLocalTree] 最终结果:', result);
    return result;
  }, [buildTreeNode]);

  const buildRemoteTree = useCallback((response) => {
    console.log('🏗️ [buildRemoteTree] 开始构建远程树...');
    console.log('📦 [buildRemoteTree] 原始响应数据:', response);
    
    const data = Array.isArray(response) ? response : response?.children || [];
    console.log('📁 [buildRemoteTree] 处理后的data:', data);
    
    const result = [{
      title: '🌐 远程仓库',
      key: 'remote_root',
      children: data
        .slice(0, 20)
        .map((item, index) => {
          const node = buildTreeNode(item, index, 'remote');
          console.log(`📄 [buildRemoteTree] 构建节点 ${index}:`, node);
          return node;
        })
    }];
    
    console.log('✅ [buildRemoteTree] 最终结果:', result);
    return result;
  }, [buildTreeNode]);

  const loadInitialData = async () => {
    console.log('🚀 [loadInitialData] 开始加载数据...');
    console.log('📋 [loadInitialData] 配置信息:', config);
    
    setLoading(true);
    setError(null);

    try {
      console.log('📡 [loadInitialData] 发送API请求...');
      const [localResponse, remoteResponse] = await Promise.all([
        http.get('/api/model-storage/lazy-load-tree/', {
          params: { path: config.basePath, root: true }
        }),
        http.get('/api/model-storage/remote-lazy-load/', {
          params: { path: config.remoteUrl }
        })
      ]);

      console.log('✅ [loadInitialData] 本地API响应:', localResponse);
      console.log('✅ [loadInitialData] 远程API响应:', remoteResponse);

      const localTree = buildLocalTree(localResponse);
      const remoteTree = buildRemoteTree(remoteResponse);
      
      console.log('🏗️ [loadInitialData] 构建的本地树数据:', localTree);
      console.log('🏗️ [loadInitialData] 构建的远程树数据:', remoteTree);

      setLocalTreeData(localTree);
      setRemoteTreeData(remoteTree);
      
      console.log('✨ [loadInitialData] 数据设置完成');
    } catch (error) {
      console.error('❌ [loadInitialData] 加载失败:', error);
      setError(error.message || '加载数据失败');
      setLocalTreeData([buildErrorNode('local')]);
      setRemoteTreeData([buildErrorNode('remote')]);
    } finally {
      setLoading(false);
      console.log('🏁 [loadInitialData] 加载流程结束');
    }
  };

  useEffect(() => {
    console.log('🔄 [useEffect] localTreeData变化:', localTreeData);
    console.log('🔄 [useEffect] remoteTreeData变化:', remoteTreeData);
    console.log('🔄 [useEffect] loading状态:', loading);
    console.log('🔄 [useEffect] error状态:', error);
  }, [localTreeData, remoteTreeData, loading, error]);

  useEffect(() => {
    if (config.basePath && config.remoteUrl) {
      console.log('⚡ [挂载/配置变化] 触发loadInitialData');
      loadInitialData();
    } else {
      console.warn('⚠️ [挂载/配置变化] 配置不完整，跳过加载');
    }
    
    if (config.compareId) {
      loadDiffInfo();
    }
  }, []);

  const onLoadData = useCallback(async (treeNode) => {
    console.log('🌲 [onLoadData] 开始加载子节点...');
    console.log('🌲 [onLoadData] treeNode:', treeNode);
    console.log('🌲 [onLoadData] treeNode.key:', treeNode.key);
    console.log('🌲 [onLoadData] treeNode.data:', treeNode.data);
    console.log('🌲 [onLoadData] treeNode.children:', treeNode.children);
    
    const { key, data } = treeNode;
    if (!data?.path || (treeNode.children && treeNode.children.length > 0)) {
      console.log('🌲 [onLoadData] 跳过加载 - 无路径或已有子节点');
      return;
    }

    try {
      const isLocal = key.startsWith('local_');
      console.log('🌲 [onLoadData] 是本地节点:', isLocal);
      console.log(`%c🌲 [onLoadData] Requesting data for path: ${data.path}`, 'color: blue; font-weight: bold; font-size: 14px;');
      
      const response = await http.get(
        isLocal ? '/api/model-storage/lazy-load-tree/' : '/api/model-storage/remote-lazy-load/',
        { params: { path: data.path } }
      );

      console.log('🌲 [onLoadData] API响应:', response);
      
      const responseData = response?.data || response?.children || response || [];
      console.log('🌲 [onLoadData] 处理后的数据:', responseData);
      
      const children = responseData?.map((item, index) => {
        const childNode = buildTreeNode(item, index, isLocal ? 'local' : 'remote');
        console.log(`🌲 [onLoadData] 构建子节点 ${index}:`, childNode);
        return childNode;
      }) || [];

      console.log('🌲 [onLoadData] 最终子节点数组:', children);
      
      if (isLocal) {
        setLocalTreeData(origin => updateTreeData(origin, key, children));
      } else {
        setRemoteTreeData(origin => updateTreeData(origin, key, children));
      }
      console.log('🌲 [onLoadData] 状态更新完成');
      
    } catch (error) {
      console.error('🌲 [onLoadData] 加载子节点失败:', error);
    }
  }, [buildTreeNode]);

  const filterTreeNode = useCallback((node, searchValue) => {
    const nodeTitle = node.title?.props?.children?.[0] || node.title;
    if (typeof nodeTitle === 'string') {
      return nodeTitle.toLowerCase().includes(searchValue.toLowerCase());
    }
    return false;
  }, []);

  const onSelect = useCallback((selectedKeys, info) => {
    console.log('📋 [Tree选择] selectedKeys:', selectedKeys, 'info:', info);
    // 说明：移除了此处的自动展开/折叠逻辑，以避免和 onExpand 事件冲突。
    // 请通过点击节点旁的 > 箭头来展开。
  }, []);

  console.log('🎨 [render] 开始渲染, loading:', loading);
  console.log('🎨 [render] localTreeData数组长度:', localTreeData?.length);
  console.log('🎨 [render] remoteTreeData数组长度:', remoteTreeData?.length);

  return (
    <div style={{ padding: '24px', minHeight: '100vh', background: '#f0f2f5' }}>
      {/* 页面头部 */}
      <Card style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Breadcrumb>
            <Breadcrumb.Item>
              <HomeOutlined />
              <span style={{ marginLeft: '8px' }}>首页</span>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <DiffOutlined />
              <span style={{ marginLeft: '8px' }}>文件对比</span>
            </Breadcrumb.Item>
          </Breadcrumb>
          
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Title level={3} style={{ margin: 0 }}>
                <DiffOutlined style={{ marginRight: '12px', color: '#1890ff' }} />
                文件树对比
              </Title>
              <p style={{ margin: '8px 0 0 0', color: '#666' }}>
                对比本地仓库与远程仓库的文件差异
              </p>
            </div>
            <Space>
              <Button 
                icon={<BarChartOutlined />} 
                onClick={handleDocStatsModal}
                loading={false}
                style={{
                  borderRadius: '8px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none',
                  color: 'white',
                  fontWeight: '500'
                }}
              >
                文档统计
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={loadInitialData}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </div>
        </Space>
      </Card>

      {/* 主要内容 */}
      <Card className={styles.container} style={{ minHeight: 'calc(100vh - 200px)' }}>
        {error && <Alert type="error" message={error} className={styles.alert} />}
        
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Search
              placeholder="搜索文件或路径..."
              value={searchValue}
              onChange={e => setSearchValue(e.target.value)}
              style={{ width: 400 }}
            />
            <Space>
              <span style={{ color: '#666' }}>
                本地: {config.basePath}
              </span>
              <span style={{ color: '#666' }}>
                远程: {config.remoteUrl}
              </span>
            </Space>
          </div>
          
          <Row gutter={16}>
            <Col span={12}>
              {/* 本地仓库 */}
              <Card 
                title={<><FolderOutlined /> 本地仓库: {config.basePath}</>}
                size="small" 
                style={{ height: '75vh', overflow: 'auto' }}
              >
                {(() => {
                  console.log('🌳 [渲染本地Tree] treeData:', localTreeData);
                  console.log('🌳 [渲染本地Tree] 长度:', localTreeData?.length);
                  console.log('🌳 [渲染本地Tree] 第一个元素:', localTreeData?.[0]);
                  return null;
                })()}
                {localTreeData && localTreeData.length > 0 ? (
                  <Tree
                    showIcon
                    defaultExpandAll={false}
                    defaultExpandedKeys={['local_root']}
                    loadData={onLoadData}
                    treeData={localTreeData}
                    onExpand={(expandedKeys, info) => {
                      console.log('🌳 [本地Tree展开] expandedKeys:', expandedKeys, 'info:', info);
                      setLocalExpandedKeys(expandedKeys);
                    }}
                    expandedKeys={localExpandedKeys}
                    onSelect={(selectedKeys, info) => {
                      console.log('📋 [本地Tree选择]:', selectedKeys, info);
                      onSelect(selectedKeys, info);
                    }}
                  />
                ) : (
                  <Empty 
                    image={Empty.PRESENTED_IMAGE_SIMPLE} 
                    description={loading ? '加载中...' : '暂无数据'} 
                  />
                )}
              </Card>
            </Col>
            
            <Col span={12}>
              {/* 远程仓库 */}
              <Card 
                title={<><CloudOutlined /> 远程仓库: {config.remoteUrl}</>}
                size="small" 
                style={{ height: '75vh', overflow: 'auto' }}
              >
                {(() => {
                  console.log('🌳 [渲染远程Tree] treeData:', remoteTreeData);
                  console.log('🌳 [渲染远程Tree] 长度:', remoteTreeData?.length);
                  console.log('🌳 [渲染远程Tree] 第一个元素:', remoteTreeData?.[0]);
                  return null;
                })()}
                {remoteTreeData && remoteTreeData.length > 0 ? (
                  <Tree
                    showIcon
                    defaultExpandAll={false}
                    defaultExpandedKeys={['remote_root']}
                    loadData={onLoadData}
                    treeData={remoteTreeData}
                    onExpand={(expandedKeys, info) => {
                      console.log('🌳 [远程Tree展开] expandedKeys:', expandedKeys, 'info:', info);
                      setRemoteExpandedKeys(expandedKeys);
                    }}
                    expandedKeys={remoteExpandedKeys}
                    onSelect={(selectedKeys, info) => {
                      console.log('📋 [远程Tree选择]:', selectedKeys, info);
                      onSelect(selectedKeys, info);
                    }}
                  />
                ) : (
                  <Empty 
                    image={Empty.PRESENTED_IMAGE_SIMPLE} 
                    description={loading ? '加载中...' : '暂无数据'} 
                  />
                )}
              </Card>
            </Col>
          </Row>
        </Space>
      </Card>
    </div>
  );
} 