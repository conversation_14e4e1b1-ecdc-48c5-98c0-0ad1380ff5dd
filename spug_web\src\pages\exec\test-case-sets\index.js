/**
 * 测试用例集管理页面
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Row,
  Col,
  Tag,
  Empty,
  Spin,
  Drawer,
  Form,
  Input,
  message,
  Space,
  Tooltip,
  Popconfirm,
  Dropdown,
  Menu,
  Upload,
  Modal,
  Checkbox,
  Progress
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  SettingOutlined,
  SaveOutlined,
  CloseOutlined,
  CheckSquareOutlined,
  UploadOutlined,
  DownloadOutlined,
  ExportOutlined,
  FileExcelOutlined,
  CheckCircleOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { http, history } from 'libs';
import TestStepsSidebar from './TestStepsSidebar';
import styles from './index.module.less';

function TestCaseSets() {
  const [caseSets, setCaseSets] = useState([]);
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [editingCaseSet, setEditingCaseSet] = useState(null);
  const [deleting, setDeleting] = useState(false);
  const [selectedCaseSets, setSelectedCaseSets] = useState([]);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [selectedCaseSet, setSelectedCaseSet] = useState(null);
  const [exporting, setExporting] = useState(false);
  const [importing, setImporting] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    loadCaseSets();
  }, []);

  const loadCaseSets = async () => {
    setLoading(true);
    try {
      const response = await http.get('/api/exec/test-case-sets/');
      console.log('Load test case sets response:', response);
      
      let caseSetsData = [];
      if (Array.isArray(response)) {
        caseSetsData = response;
      } else if (response && Array.isArray(response.data)) {
        caseSetsData = response.data;
      } else {
        console.warn('API返回的数据格式不正确:', response);
        caseSetsData = [];
      }
      
      setCaseSets(caseSetsData);
      console.log('设置的测试用例集数据:', caseSetsData);
    } catch (error) {
      console.error('Load test case sets error:', error);
      message.error('加载测试用例集失败');
      setCaseSets([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCaseSet = () => {
    // 跳转到新建页面
    history.push('/exec/test-case-set/new');
  };

  const handleEditCaseSet = (caseSet) => {
    setEditingCaseSet(caseSet);
    form.setFieldsValue({
      name: caseSet.name,
      description: caseSet.description,
      category: caseSet.category
    });
    setDrawerVisible(true);
  };

  const handleSaveCaseSet = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingCaseSet) {
        // 更新测试用例集
        await http.put(`/api/exec/test-case-sets/${editingCaseSet.id}/`, {
          ...editingCaseSet,
          ...values
        });
        message.success('测试用例集更新成功');
      } else {
        // 创建新测试用例集
        const newCaseSet = {
          ...values,
          test_cases: []
        };
        await http.post('/api/exec/test-case-sets/', newCaseSet);
        message.success('测试用例集创建成功');
      }
      
      setDrawerVisible(false);
      loadCaseSets();
    } catch (error) {
      message.error('保存失败');
      console.error('Save test case set error:', error);
    }
  };

  const handleDeleteCaseSet = async (caseSetId) => {
    try {
      setDeleting(true);
      await http.delete(`/api/exec/test-case-sets/${caseSetId}/`);
      message.success('删除成功');
      await loadCaseSets();
      setTimeout(() => setDeleting(false), 1000);
    } catch (error) {
      message.error('删除失败');
      console.error('Delete test case set error:', error);
      setDeleting(false);
    }
  };

  const handleCopyCaseSet = async (caseSet) => {
    try {
      const newCaseSet = {
        ...caseSet,
        name: `${caseSet.name} - 副本`,
        id: undefined
      };
      await http.post('/api/exec/test-case-sets/', newCaseSet);
      message.success('复制成功');
      loadCaseSets();
    } catch (error) {
      message.error('复制失败');
      console.error('Copy test case set error:', error);
    }
  };

  const handleEditTestCases = (caseSet) => {
    // 跳转到编辑页面
    history.push(`/exec/test-case-set/${caseSet.id}`);
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    try {
      message.loading('正在下载模板...', 0);
      const response = await fetch('/api/exec/test-case-sets-import-export/?action=template', {
        method: 'GET',
        headers: {
          'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'X-Token': localStorage.getItem('token')
        }
      });

      if (!response.ok) {
        throw new Error(`下载失败: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = '测试用例集导入模板.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      message.destroy();
      message.success('模板下载成功');
    } catch (error) {
      message.destroy();
      message.error('下载模板失败: ' + error.message);
      console.error('Download template error:', error);
    }
  };

  // 导入功能
  const handleImport = ({ file }) => {
    const formData = new FormData();
    formData.append('file', file);

    setImporting(true);
    message.loading('正在导入测试用例集...', 0);

    fetch('/api/exec/test-case-sets-import-export/', {
      method: 'POST',
      headers: {
        'X-Token': localStorage.getItem('token')
      },
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      message.destroy();
      if (data.error) {
        message.error('导入失败: ' + data.error);
      } else {
        message.success(data.message || '导入成功');
        loadCaseSets(); // 重新加载数据
      }
    })
    .catch(error => {
      message.destroy();
      message.error('导入失败: ' + error.message);
      console.error('Import error:', error);
    })
    .finally(() => {
      setImporting(false);
    });

    return false; // 阻止默认上传行为
  };

  // 导出功能
  const handleExport = () => {
    if (selectedCaseSets.length === 0) {
      message.warning('请先选择要导出的测试用例集');
      return;
    }
    setExportModalVisible(true);
  };

  // 执行导出
  const executeExport = () => {
    setExporting(true);
    setExportModalVisible(false);

    const caseSetIds = selectedCaseSets.join(',');
    const url = `/api/exec/test-case-sets-import-export/?case_set_ids=${caseSetIds}`;

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `test_case_sets_export_${timestamp}.xlsx`;

    const token = localStorage.getItem('token');
    if (!token) {
      message.error('认证信息不存在，请重新登录');
      setExporting(false);
      return;
    }

    fetch(url, {
      method: 'GET',
      headers: {
        'X-Token': token
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`导出失败: ${response.status}`);
      }
      return response.blob();
    })
    .then(blob => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      message.success('导出成功');
      setSelectedCaseSets([]); // 清空选择
    })
    .catch(error => {
      message.error('导出失败: ' + error.message);
      console.error('Export error:', error);
    })
    .finally(() => {
      setExporting(false);
    });
  };

  // 全选/取消全选
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedCaseSets(caseSets.map(cs => cs.id));
    } else {
      setSelectedCaseSets([]);
    }
  };

  // 单选
  const handleSelectCaseSet = (caseSetId, checked) => {
    if (checked) {
      setSelectedCaseSets([...selectedCaseSets, caseSetId]);
    } else {
      setSelectedCaseSets(selectedCaseSets.filter(id => id !== caseSetId));
    }
  };

  const getCaseSetIcon = (category) => {
    const iconMap = {
      functional: <CheckSquareOutlined style={{ color: '#52c41a' }} />,
      performance: <SettingOutlined style={{ color: '#1890ff' }} />,
      security: <SettingOutlined style={{ color: '#fa541c' }} />,
      integration: <SettingOutlined style={{ color: '#722ed1' }} />
    };
    return iconMap[category] || <SettingOutlined style={{ color: '#666' }} />;
  };

  // 处理测试用例集点击
  const handleCaseSetClick = (caseSet) => {
    setSelectedCaseSet(caseSet);
    setSidebarVisible(true);
  };

  // 更新测试用例集进度
  const handleCaseSetUpdate = (updatedCaseSet) => {
    setSelectedCaseSet(updatedCaseSet);
    // 刷新用例集列表以显示最新进度
    loadCaseSets();
  };

  const renderCard = (caseSet) => {
    const isSelected = selectedCaseSets.includes(caseSet.id);

    const caseSetActions = [
      <Tooltip title="编辑测试用例" key="edit">
        <EditOutlined
          onClick={(e) => {
            e.stopPropagation();
            handleEditTestCases(caseSet);
          }}
        />
      </Tooltip>,
      <Tooltip title="编辑基本信息" key="settings">
        <SettingOutlined
          onClick={(e) => {
            e.stopPropagation();
            handleEditCaseSet(caseSet);
          }}
        />
      </Tooltip>,
      <Tooltip title="复制" key="copy">
        <CopyOutlined
          onClick={(e) => {
            e.stopPropagation();
            handleCopyCaseSet(caseSet);
          }}
        />
      </Tooltip>,
      <Tooltip title="删除" key="delete">
        <Popconfirm
          title="确定要删除这个测试用例集吗？"
          onConfirm={(e) => {
            e && e.stopPropagation();
            handleDeleteCaseSet(caseSet.id);
          }}
          onCancel={(e) => e && e.stopPropagation()}
          okText="删除"
          cancelText="取消"
          overlayStyle={{ zIndex: 1050 }}
        >
          <DeleteOutlined
            style={{ color: '#ff4d4f' }}
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          />
        </Popconfirm>
      </Tooltip>
    ];

    return (
      <div key={caseSet.id} style={{ position: 'relative' }}>
        <Checkbox
          checked={isSelected}
          onChange={(e) => handleSelectCaseSet(caseSet.id, e.target.checked)}
          style={{
            position: 'absolute',
            top: 8,
            right: 8,
            zIndex: 10
          }}
          onClick={(e) => e.stopPropagation()}
        />
        <Card
          className={styles.caseSetCard}
          hoverable
          actions={caseSetActions}
          style={{
            border: isSelected ? '2px solid #1890ff' : '1px solid #d9d9d9'
          }}
        >
          <Card.Meta
            avatar={getCaseSetIcon(caseSet.category)}
            title={
              <div className={styles.cardTitle}>
                <div
                  style={{
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCaseSetClick(caseSet);
                  }}
                >
                  <FileTextOutlined style={{ color: '#1890ff' }} />
                  <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                    {caseSet.name}
                  </span>
                  <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '12px' }} />
                </div>
                {caseSet.progress_percentage !== undefined && (
                  <div style={{ marginTop: 4 }}>
                    <Progress
                      percent={caseSet.progress_percentage}
                      size="small"
                      strokeColor="#52c41a"
                      showInfo={false}
                    />
                    <div style={{ fontSize: '11px', color: '#666', textAlign: 'center' }}>
                      进度: {caseSet.progress_percentage}%
                    </div>
                  </div>
                )}
              </div>
            }
            description={
              <div className={styles.cardContent}>
                <p className={styles.description}>{caseSet.description}</p>
                <div className={styles.caseCount}>
                  <Tag color="blue">
                    📝 {caseSet.test_cases?.length || 0} 个用例
                  </Tag>
                  {caseSet.category && (
                    <Tag color="green">{caseSet.category}</Tag>
                  )}
                </div>
                <div className={styles.casePreview}>
                  {caseSet.test_cases?.slice(0, 3).map((testCase, index) => (
                    <Tag key={index} size="small" style={{ marginBottom: 4 }}>
                      {testCase.name || `用例${index + 1}`}
                    </Tag>
                  ))}
                  {caseSet.test_cases?.length > 3 && (
                    <Tag size="small" style={{ marginBottom: 4 }}>
                      +{caseSet.test_cases.length - 3} 更多
                    </Tag>
                  )}
                </div>
                <div style={{ marginTop: 8, fontSize: '12px', color: '#999', textAlign: 'center' }}>
                  使用底部按钮操作测试用例集
                </div>
              </div>
            }
          />
        </Card>
      </div>
    );
  };

  return (
    <div className={styles.testCaseSetsPage}>
      <Card
        title={
          <Space>
            <span>测试用例集</span>
            {caseSets.length > 0 && (
              <Checkbox
                checked={selectedCaseSets.length === caseSets.length}
                indeterminate={selectedCaseSets.length > 0 && selectedCaseSets.length < caseSets.length}
                onChange={(e) => handleSelectAll(e.target.checked)}
              >
                全选 ({selectedCaseSets.length}/{caseSets.length})
              </Checkbox>
            )}
          </Space>
        }
        extra={
          <Space>
            <Button
              type="default"
              icon={<DownloadOutlined />}
              onClick={handleDownloadTemplate}
            >
              下载模板
            </Button>
            <Upload
              name="file"
              showUploadList={false}
              customRequest={handleImport}
              beforeUpload={file => {
                const isXlsx = file.name.endsWith('.xlsx') || file.name.endsWith('.xls');
                if (!isXlsx) {
                  message.error('只能上传 .xlsx 或 .xls 格式的文件');
                }
                return isXlsx;
              }}
              disabled={importing}
            >
              <Button
                icon={<UploadOutlined />}
                loading={importing}
              >
                从Excel导入
              </Button>
            </Upload>
            <Button
              type="default"
              icon={<ExportOutlined />}
              onClick={handleExport}
              disabled={selectedCaseSets.length === 0}
              loading={exporting}
            >
              导出选中 ({selectedCaseSets.length})
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateCaseSet}
            >
              新建测试用例集
            </Button>
          </Space>
        }
      >
        <Spin spinning={loading}>
          {caseSets.length === 0 ? (
            <Empty
              description="暂无测试用例集"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateCaseSet}>
                创建第一个测试用例集
              </Button>
            </Empty>
          ) : (
            <Row gutter={[16, 16]}>
              {caseSets.map(caseSet => (
                <Col xs={24} sm={12} md={8} lg={6} xl={6} key={caseSet.id}>
                  {renderCard(caseSet)}
                </Col>
              ))}
            </Row>
          )}
        </Spin>
      </Card>

      {/* 新建/编辑用例集侧边栏 */}
      <Drawer
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span>{editingCaseSet ? '编辑测试用例集' : '新建测试用例集'}</span>
            <Space>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSaveCaseSet}
              >
                保存用例集
              </Button>
              <Button
                icon={<CloseOutlined />}
                onClick={() => setDrawerVisible(false)}
              >
                关闭
              </Button>
            </Space>
          </div>
        }
        placement="right"
        size="default"
        visible={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        maskClosable={false}
        destroyOnClose={true}
        bodyStyle={{ padding: '16px' }}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="测试用例集名称"
            rules={[{ required: true, message: '请输入测试用例集名称' }]}
          >
            <Input placeholder="例如: 用户登录功能测试用例集" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="测试用例集描述"
            rules={[{ required: true, message: '请输入测试用例集描述' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="描述这个测试用例集的用途和功能..."
            />
          </Form.Item>

          <Form.Item
            name="category"
            label="分类"
          >
            <Input placeholder="例如: functional, performance, security, integration" />
          </Form.Item>
        </Form>
      </Drawer>

      {/* 导出确认模态框 */}
      <Modal
        title="导出测试用例集"
        visible={exportModalVisible}
        onOk={executeExport}
        onCancel={() => setExportModalVisible(false)}
        okText="确认导出"
        cancelText="取消"
        confirmLoading={exporting}
      >
        <p>确定要导出选中的 {selectedCaseSets.length} 个测试用例集吗？</p>
        <p>导出的Excel文件将包含所有选中用例集的详细信息。</p>
      </Modal>

      {/* 测试步骤侧边栏 */}
      <TestStepsSidebar
        visible={sidebarVisible}
        onClose={() => setSidebarVisible(false)}
        caseSet={selectedCaseSet}
        onUpdate={handleCaseSetUpdate}
      />
    </div>
  );
}

export default TestCaseSets;
