# Spug Backend Libraries Reference

## Table of Contents

1. [SSH Library](#ssh-library)
2. [Parser Library](#parser-library)
3. [Decorators](#decorators)
4. [Utilities](#utilities)
5. [Validators](#validators)
6. [Mixins](#mixins)
7. [Mail & Notifications](#mail--notifications)
8. [Git Library](#git-library)
9. [Database Utilities](#database-utilities)

---

## SSH Library

**Location**: `libs/ssh.py`

Comprehensive SSH management library for remote host operations.

### SSH Class

```python
class SSH:
    def __init__(self, hostname, port=22, username='root', pkey=None, 
                 password=None, default_env=None, connect_timeout=10, term=None)
```

#### Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `hostname` | `str` | - | Target host IP or domain |
| `port` | `int` | `22` | SSH port |
| `username` | `str` | `'root'` | SSH username |
| `pkey` | `str/RSAKey` | `None` | Private key for authentication |
| `password` | `str` | `None` | Password for authentication |
| `default_env` | `dict` | `None` | Default environment variables |
| `connect_timeout` | `int` | `10` | Connection timeout in seconds |
| `term` | `dict` | `None` | Terminal configuration |

#### Methods

##### Connection Management

```python
def get_client(self):
    """Get or create SSH client connection"""
    
def ping(self):
    """Test connection - returns True if successful"""
    
def add_public_key(self, public_key):
    """Add public key to authorized_keys"""
```

##### Command Execution

```python
def exec_command_raw(self, command, environment=None):
    """
    Execute command and return (exit_code, output)
    
    Args:
        command (str): Command to execute
        environment (dict): Environment variables
        
    Returns:
        tuple: (exit_code, output_string)
    """

def exec_command_raw_with_timeout(self, command, environment=None, timeout=900):
    """
    Execute command with timeout protection
    
    Args:
        command (str): Command to execute
        environment (dict): Environment variables
        timeout (int): Timeout in seconds
        
    Returns:
        tuple: (exit_code, output_string)
    """

def exec_command(self, command, environment=None):
    """Execute interactive command in shell session"""

def exec_command_with_stream(self, command, environment=None):
    """
    Execute command with real-time output streaming
    
    Yields:
        tuple: (exit_code, output_chunk)
    """
```

##### File Operations

```python
def put_file(self, local_path, remote_path, callback=None):
    """Upload file to remote host"""

def put_file_by_fl(self, file_obj, remote_path, callback=None):
    """Upload file object to remote host"""

def list_dir_attr(self, path):
    """List directory contents with attributes"""

def sftp_stat(self, path):
    """Get file/directory statistics"""

def remove_file(self, path):
    """Remove remote file"""
```

##### Utility Methods

```python
@staticmethod
def generate_key():
    """
    Generate RSA key pair
    
    Returns:
        tuple: (private_key_string, public_key_string)
    """
```

#### Usage Examples

##### Basic Connection and Command Execution

```python
from libs.ssh import SSH

# Using private key authentication
with SSH('*************', username='root', pkey=private_key) as ssh:
    # Test connection
    if ssh.ping():
        print("Connection successful")
    
    # Execute simple command
    exit_code, output = ssh.exec_command_raw('ls -la /var/log')
    if exit_code == 0:
        print("Command output:", output)
    else:
        print("Command failed:", output)

# Using password authentication
with SSH('*************', username='admin', password='secret') as ssh:
    exit_code, output = ssh.exec_command_raw('uptime')
```

##### Command Execution with Environment Variables

```python
env_vars = {
    'DEPLOYMENT_ENV': 'production',
    'API_KEY': 'secret-key-123',
    'DEBUG': 'false'
}

with SSH(host, username='deploy', pkey=deploy_key) as ssh:
    # Execute deployment script with environment
    exit_code, output = ssh.exec_command_raw(
        './deploy.sh',
        environment=env_vars
    )
```

##### Real-time Command Streaming

```python
def deploy_with_progress(host, script_content):
    with SSH(host, username='root', pkey=private_key) as ssh:
        # Upload deployment script
        script_file = StringIO(script_content)
        ssh.put_file_by_fl(script_file, '/tmp/deploy.sh')
        
        # Execute with real-time output
        for exit_code, output_chunk in ssh.exec_command_with_stream('bash /tmp/deploy.sh'):
            if output_chunk:
                print(f"[{host}] {output_chunk}", end='')
            
            # Send progress to frontend via websocket
            send_progress_update(host, output_chunk)
        
        print(f"Deployment finished with exit code: {exit_code}")
```

##### File Upload with Progress Callback

```python
def upload_with_progress(local_file, remote_path, host_config):
    def progress_callback(transferred, total):
        percent = (transferred / total) * 100
        print(f"Upload progress: {percent:.1f}%")
    
    with SSH(**host_config) as ssh:
        ssh.put_file(local_file, remote_path, callback=progress_callback)
```

##### Timeout-Protected Execution

```python
def safe_command_execution(ssh, command, max_timeout=300):
    """Execute command with timeout protection"""
    try:
        exit_code, output = ssh.exec_command_raw_with_timeout(
            command, 
            timeout=max_timeout
        )
        
        if exit_code == -2:
            # Command timed out
            return {'status': 'timeout', 'message': output}
        elif exit_code == 0:
            return {'status': 'success', 'output': output}
        else:
            return {'status': 'error', 'exit_code': exit_code, 'output': output}
            
    except Exception as e:
        return {'status': 'exception', 'error': str(e)}
```

---

## Parser Library

**Location**: `libs/parser.py`

Request data parsing and validation library for Django views.

### JsonParser Class

```python
class JsonParser:
    def __init__(self, *arguments)
```

#### Argument Class

```python
class Argument:
    def __init__(self, name, type=str, required=True, default=None, 
                 help=None, choices=None, filter=None, handler=None)
```

##### Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `name` | `str` | - | Argument name |
| `type` | `type` | `str` | Data type (str, int, float, bool, list, dict) |
| `required` | `bool` | `True` | Whether argument is required |
| `default` | `any` | `None` | Default value if not provided |
| `help` | `str` | `None` | Error message |
| `choices` | `list` | `None` | Valid choices |
| `filter` | `function` | `None` | Custom filter function |
| `handler` | `function` | `None` | Custom handler function |

#### Usage Examples

##### Basic Validation

```python
from libs.parser import JsonParser, Argument

def create_host(request):
    form, error = JsonParser(
        Argument('name', help='Host name is required'),
        Argument('hostname', help='IP address is required'),
        Argument('port', type=int, default=22),
        Argument('username', default='root'),
        Argument('desc', required=False)
    ).parse(request.body)
    
    if error:
        return JsonResponse(error, status=400)
    
    # Create host with validated data
    host = Host.objects.create(**form, created_by=request.user)
    return JsonResponse(host.to_dict())
```

##### Advanced Validation with Choices and Filters

```python
def create_deployment(request):
    form, error = JsonParser(
        Argument('app_id', type=int, help='Application ID is required'),
        Argument('environment', choices=['dev', 'test', 'prod'], 
                help='Invalid environment'),
        Argument('version', help='Version is required'),
        Argument('config', type=dict, required=False, default={}),
        Argument('notify_users', type=list, default=[]),
        Argument('auto_rollback', type=bool, default=True),
        Argument('timeout', type=int, default=300, 
                filter=lambda x: max(60, min(x, 3600)))  # Clamp between 60-3600
    ).parse(request.body)
    
    if error:
        return JsonResponse(error, status=400)
    
    # Validate app exists and user has permission
    app = App.objects.filter(id=form.app_id).first()
    if not app:
        return JsonResponse({'error': 'Application not found'}, status=404)
    
    # Create deployment
    deployment = Deployment.objects.create(**form, created_by=request.user)
    return JsonResponse(deployment.to_dict())
```

##### Custom Validation Functions

```python
def validate_ip_address(value):
    """Custom IP address validator"""
    import ipaddress
    try:
        ipaddress.ip_address(value)
        return value
    except ValueError:
        raise ValueError('Invalid IP address format')

def validate_script_content(content):
    """Validate script content"""
    if len(content) > 100000:  # 100KB limit
        raise ValueError('Script too large (max 100KB)')
    
    # Check for dangerous commands
    dangerous_patterns = ['rm -rf /', 'mkfs', 'dd if=']
    content_lower = content.lower()
    for pattern in dangerous_patterns:
        if pattern in content_lower:
            raise ValueError(f'Potentially dangerous command detected: {pattern}')
    
    return content

def create_script(request):
    form, error = JsonParser(
        Argument('name', help='Script name is required'),
        Argument('content', handler=validate_script_content),
        Argument('interpreter', choices=['bash', 'python', 'powershell']),
        Argument('timeout', type=int, default=300),
        Argument('target_hosts', type=list, help='Target hosts required')
    ).parse(request.body)
    
    if error:
        return JsonResponse(error, status=400)
    
    # Additional validation for host IDs
    host_ids = form.target_hosts
    valid_hosts = Host.objects.filter(id__in=host_ids).count()
    if valid_hosts != len(host_ids):
        return JsonResponse({'error': 'Some host IDs are invalid'}, status=400)
```

---

## Decorators

**Location**: `libs/decorators.py`

Authentication and authorization decorators for Django views.

### Available Decorators

#### @auth_required

```python
from libs.decorators import auth_required

@auth_required
def protected_view(request):
    """View requiring user authentication"""
    return JsonResponse({'user': request.user.username})
```

#### @auth (Permission-based)

```python
from libs.decorators import auth

@auth('host.host.view')
def host_list(request):
    """View requiring specific permission"""
    hosts = Host.objects.all()
    return JsonResponse([h.to_dict() for h in hosts])

@auth('host.host.add|host.host.edit')
def host_manage(request):
    """View requiring any of the specified permissions (OR)"""
    pass

@auth('host.host.view&system.account.view')  
def admin_view(request):
    """View requiring all specified permissions (AND)"""
    pass
```

#### Combined Usage

```python
from libs.decorators import auth_required, auth
from libs.parser import JsonParser, Argument

@auth_required
@auth('deploy.app.deploy')
def deploy_application(request):
    form, error = JsonParser(
        Argument('app_id', type=int),
        Argument('environment', choices=['dev', 'test', 'prod']),
        Argument('version', help='Version is required')
    ).parse(request.body)
    
    if error:
        return JsonResponse(error, status=400)
    
    # Trigger deployment
    deployment = trigger_deployment(
        app_id=form.app_id,
        environment=form.environment,
        version=form.version,
        user=request.user
    )
    
    return JsonResponse(deployment.to_dict())
```

---

## Utilities

**Location**: `libs/utils.py`

Common utility functions and classes.

### Key Functions

#### Time Utilities

```python
def human_datetime(dt=None):
    """
    Get human-readable datetime string
    
    Args:
        dt (datetime): Optional datetime object
        
    Returns:
        str: Formatted datetime string (YYYY-MM-DD HH:MM:SS)
    """

# Usage
from libs.utils import human_datetime

timestamp = human_datetime()  # Current time
custom_time = human_datetime(datetime(2023, 12, 1, 14, 30))
```

#### AttrDict Class

```python
class AttrDict(dict):
    """Dictionary with attribute access"""
    
    def __getattr__(self, key):
        return self[key]
    
    def __setattr__(self, key, value):
        self[key] = value

# Usage
config = AttrDict({
    'database': {
        'host': 'localhost',
        'port': 5432
    },
    'debug': True
})

print(config.database.host)  # 'localhost'
print(config.debug)          # True
config.new_setting = 'value'
```

#### JSON Response Helper

```python
def json_response(data=None, error=None):
    """
    Standard JSON response format
    
    Args:
        data: Response data
        error: Error message
        
    Returns:
        JsonResponse: Formatted response
    """

# Usage in views
def my_view(request):
    try:
        result = perform_operation()
        return json_response(data=result)
    except Exception as e:
        return json_response(error=str(e))
```

#### Configuration Management

```python
def load_config(config_path):
    """Load configuration from file"""
    
def get_env_var(key, default=None, required=False):
    """Get environment variable with validation"""
    
def merge_configs(*configs):
    """Merge multiple configuration dictionaries"""

# Usage
base_config = load_config('config/base.json')
env_config = load_config('config/production.json')
final_config = merge_configs(base_config, env_config)

api_key = get_env_var('API_KEY', required=True)
debug_mode = get_env_var('DEBUG', default=False)
```

---

## Validators

**Location**: `libs/validators.py`

Data validation utilities.

### Common Validators

```python
def validate_hostname(hostname):
    """Validate hostname or IP address"""
    
def validate_port(port):
    """Validate port number (1-65535)"""
    
def validate_email(email):
    """Validate email address format"""
    
def validate_script_safety(script_content):
    """Check script for potentially dangerous commands"""

# Usage in views
from libs.validators import validate_hostname, validate_port

def add_host(request):
    form, error = JsonParser(
        Argument('hostname', handler=validate_hostname),
        Argument('port', type=int, handler=validate_port)
    ).parse(request.body)
```

---

## Mixins

**Location**: `libs/mixins.py`

Django model mixins for common functionality.

### ModelMixin

```python
class ModelMixin:
    """Common model methods"""
    
    def to_dict(self, excludes=None, selects=None):
        """Convert model instance to dictionary"""
        
    def to_json(self, **kwargs):
        """Convert model instance to JSON string"""

# Usage in models
from libs.mixins import ModelMixin

class Host(models.Model, ModelMixin):
    name = models.CharField(max_length=100)
    hostname = models.CharField(max_length=50)
    
    def to_view(self):
        """Custom view representation"""
        data = self.to_dict()
        data['display_name'] = f"{self.name} ({self.hostname})"
        return data
```

---

## Mail & Notifications

**Location**: `libs/mail.py`, `libs/push.py`

Email and push notification utilities.

### Email Functions

```python
def send_mail(subject, message, recipient_list, html_message=None):
    """Send email notification"""

def send_template_mail(template_name, context, recipient_list):
    """Send email using template"""

# Usage
from libs.mail import send_mail, send_template_mail

# Simple email
send_mail(
    subject='Deployment Complete',
    message='Your application has been deployed successfully',
    recipient_list=['<EMAIL>']
)

# Template email
send_template_mail(
    template_name='deployment_notification.html',
    context={
        'app_name': 'MyApp',
        'environment': 'production',
        'version': '1.2.3'
    },
    recipient_list=['<EMAIL>']
)
```

### Push Notifications

```python
def send_push_notification(user_ids, title, message, data=None):
    """Send push notification to users"""

# Usage
from libs.push import send_push_notification

send_push_notification(
    user_ids=[1, 2, 3],
    title='Deployment Alert',
    message='Production deployment failed',
    data={'deployment_id': 123, 'severity': 'high'}
)
```

---

## Git Library

**Location**: `libs/gitlib.py`

Git repository management utilities.

### GitRepository Class

```python
class GitRepository:
    def __init__(self, repo_path, remote_url=None):
        """Initialize git repository manager"""
    
    def clone(self, branch=None):
        """Clone repository"""
    
    def pull(self, branch='master'):
        """Pull latest changes"""
    
    def get_commits(self, branch='master', limit=10):
        """Get commit history"""
    
    def get_branches(self):
        """Get list of branches"""
    
    def checkout(self, branch_or_commit):
        """Checkout branch or commit"""

# Usage
from libs.gitlib import GitRepository

repo = GitRepository(
    repo_path='/var/repos/myapp',
    remote_url='https://github.com/user/myapp.git'
)

# Clone repository
repo.clone(branch='main')

# Get recent commits
commits = repo.get_commits(limit=5)
for commit in commits:
    print(f"{commit.sha}: {commit.message}")

# Deploy specific version
repo.checkout('v1.2.3')
```

---

## Database Utilities

**Location**: `libs/sqlite_backend.py`

Custom SQLite backend with additional features.

### Usage in Settings

```python
# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'libs.sqlite_backend',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}
```

---

## Error Handling Patterns

### Standard Error Response

```python
from libs.utils import json_response

def safe_view(request):
    try:
        # View logic here
        result = perform_operation()
        return json_response(data=result)
    
    except ValidationError as e:
        return json_response(error=str(e), status=400)
    
    except PermissionError as e:
        return json_response(error='Permission denied', status=403)
    
    except Exception as e:
        logger.exception('Unexpected error in safe_view')
        return json_response(error='Internal server error', status=500)
```

### Decorator for Error Handling

```python
def handle_exceptions(view_func):
    """Decorator for standardized error handling"""
    @wraps(view_func)
    def wrapper(*args, **kwargs):
        try:
            return view_func(*args, **kwargs)
        except ValidationError as e:
            return json_response(error=str(e), status=400)
        except Exception as e:
            logger.exception(f'Error in {view_func.__name__}')
            return json_response(error='Internal server error', status=500)
    return wrapper

# Usage
@handle_exceptions
@auth('host.host.view')
def host_list(request):
    hosts = Host.objects.all()
    return json_response(data=[h.to_dict() for h in hosts])
```

---

## Best Practices

### 1. SSH Connection Management

```python
# Use context managers for automatic cleanup
with SSH(hostname, username='root', pkey=private_key) as ssh:
    result = ssh.exec_command_raw('ls -la')

# For multiple operations, reuse connections
ssh = SSH(hostname, username='root', pkey=private_key)
try:
    ssh.get_client()
    result1 = ssh.exec_command_raw('command1')
    result2 = ssh.exec_command_raw('command2')
finally:
    ssh.client.close()
```

### 2. Input Validation

```python
# Always validate user input
form, error = JsonParser(
    Argument('name', help='Name is required'),
    Argument('value', type=int, help='Value must be integer')
).parse(request.body)

if error:
    return JsonResponse(error, status=400)
```

### 3. Permission Checking

```python
# Use decorators for view-level permissions
@auth('resource.action')
def my_view(request):
    pass

# Use hasPermission for conditional logic
from libs.functools import hasPermission

if hasPermission('admin.user.manage'):
    # Show admin interface
    pass
```

### 4. Logging

```python
import logging

logger = logging.getLogger(__name__)

def deploy_application(request):
    logger.info(f'Starting deployment for user {request.user.username}')
    try:
        # Deployment logic
        logger.info('Deployment completed successfully')
    except Exception as e:
        logger.error(f'Deployment failed: {e}', exc_info=True)
        raise
```