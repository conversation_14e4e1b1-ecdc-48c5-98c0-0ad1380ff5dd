.statisticsContainer {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
  
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.statisticsCard,
.overviewCard,
.personnelCard,
.gpuCard,
.riskCard,
.testCaseCard {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
  }
  
  :global(.ant-card-head) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px 16px 0 0;
    
    :global(.ant-card-head-title) {
      color: rgba(255, 255, 255, 0.9);
      font-weight: 600;
      font-size: 16px;
    }
  }
  
  :global(.ant-card-body) {
    color: rgba(255, 255, 255, 0.8);
  }
  
  // 统计数字样式
  :global(.ant-statistic) {
    :global(.ant-statistic-title) {
      color: rgba(255, 255, 255, 0.7);
      font-size: 14px;
      margin-bottom: 8px;
    }
    
    :global(.ant-statistic-content) {
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  // 进度条样式
  :global(.ant-progress-circle) {
    :global(.ant-progress-text) {
      color: rgba(255, 255, 255, 0.9) !important;
      font-weight: 600;
    }
  }
  
  // 列表样式
  :global(.ant-list-item) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 8px 0;
    
    &:last-child {
      border-bottom: none;
    }
    
    :global(.ant-list-item-meta-title) {
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
    }
    
    :global(.ant-list-item-meta-description) {
      color: rgba(255, 255, 255, 0.6);
    }
  }
  
  // 分割线样式
  :global(.ant-divider) {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 16px 0;
  }
  
  // 标题样式
  :global(.ant-typography) {
    color: rgba(255, 255, 255, 0.9) !important;
    
    &:global(.ant-typography-title) {
      color: rgba(255, 255, 255, 0.9) !important;
      margin-bottom: 12px !important;
    }
  }
  
  // 标签样式
  :global(.ant-tag) {
    border-radius: 12px;
    font-size: 12px;
    padding: 2px 8px;
    border: none;
    
    &:global(.ant-tag-blue) {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      color: white;
    }
    
    &:global(.ant-tag-red) {
      background: linear-gradient(135deg, #ff4d4f, #ff7875);
      color: white;
    }
    
    &:global(.ant-tag-orange) {
      background: linear-gradient(135deg, #fa8c16, #ffa940);
      color: white;
    }
    
    &:global(.ant-tag-green) {
      background: linear-gradient(135deg, #52c41a, #73d13d);
      color: white;
    }
  }
  
  // 头像样式
  :global(.ant-avatar) {
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 特定卡片的个性化样式
.overviewCard {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(64, 169, 255, 0.05));
  border-color: rgba(24, 144, 255, 0.3);
}

.personnelCard {
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(115, 209, 61, 0.05));
  border-color: rgba(82, 196, 26, 0.3);
}

.gpuCard {
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.1), rgba(255, 169, 64, 0.05));
  border-color: rgba(250, 173, 20, 0.3);
}

.riskCard {
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.1), rgba(255, 120, 117, 0.05));
  border-color: rgba(255, 77, 79, 0.3);
}

.testCaseCard {
  background: linear-gradient(135deg, rgba(114, 46, 209, 0.1), rgba(147, 51, 234, 0.05));
  border-color: rgba(114, 46, 209, 0.3);
}

// 响应式设计
@media (max-width: 768px) {
  .statisticsContainer {
    padding-right: 0;
  }
  
  .statisticsCard,
  .overviewCard,
  .personnelCard,
  .gpuCard,
  .riskCard,
  .testCaseCard {
    margin-bottom: 12px;
    border-radius: 12px;
    
    :global(.ant-card-body) {
      padding: 16px;
    }
    
    :global(.ant-statistic-content) {
      font-size: 20px;
    }
  }
}

// 加载动画
.statisticsCard {
  :global(.ant-spin-container) {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 数据为空时的样式
.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.5);
  
  :global(.anticon) {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.3;
  }
  
  p {
    font-size: 16px;
    margin: 0;
  }
}

// 悬浮效果增强
.statisticsCard:hover,
.overviewCard:hover,
.personnelCard:hover,
.gpuCard:hover,
.riskCard:hover,
.testCaseCard:hover {
  :global(.ant-statistic-content) {
    transform: scale(1.05);
    transition: transform 0.2s ease;
  }
  
  :global(.ant-progress-circle) {
    transform: scale(1.02);
    transition: transform 0.2s ease;
  }
}
