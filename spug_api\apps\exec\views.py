# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.

# 导入数据库模型
from .models import TestPlan, TestCaseSet, TestPlanExecution, TestPlanStepResult

from django.views.generic import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django_redis import get_redis_connection
from django.conf import settings
from libs import json_response, JsonParser, Argument, human_datetime, auth
from apps.exec.models import ExecTemplate, ExecHistory
from apps.host.models import Host
from apps.account.utils import has_host_perm
from libs.utils import str_decode
import uuid
import json
import os


class TemplateView(View):
    @auth('exec.template.view|exec.task.do|schedule.schedule.add|schedule.schedule.edit|\
    monitor.monitor.add|monitor.monitor.edit')
    def get(self, request):
        templates = ExecTemplate.objects.all()
        types = [x['type'] for x in templates.order_by('type').values('type').distinct()]
        return json_response({'types': types, 'templates': [x.to_view() for x in templates]})

    @auth('exec.template.add|exec.template.edit')
    def post(self, request):
        form, error = JsonParser(
            Argument('id', type=int, required=False),
            Argument('name', help='请输入模版名称'),
            Argument('type', help='请选择模版类型'),
            Argument('body', help='请输入模版内容'),
            Argument('interpreter', default='sh'),
            Argument('host_ids', type=list, handler=json.dumps, default=[]),
            Argument('parameters', type=list, handler=json.dumps, default=[]),
            Argument('desc', required=False)
        ).parse(request.body)
        if error is None:
            if form.id:
                form.updated_at = human_datetime()
                form.updated_by = request.user
                ExecTemplate.objects.filter(pk=form.pop('id')).update(**form)
            else:
                form.created_by = request.user
                ExecTemplate.objects.create(**form)
        return json_response(error=error)

    @auth('exec.template.del')
    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象')
        ).parse(request.GET)
        if error is None:
            ExecTemplate.objects.filter(pk=form.id).delete()
        return json_response(error=error)


class TaskView(View):
    @auth('exec.task.do')
    def get(self, request):
        records = ExecHistory.objects.filter(user=request.user).select_related('template')
        return json_response([x.to_view() for x in records])

    @auth('exec.task.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_ids', type=list, filter=lambda x: len(x), help='请选择执行主机'),
            Argument('command', help='请输入执行命令内容'),
            Argument('interpreter', default='sh'),
            Argument('template_id', type=int, required=False),
            Argument('params', type=dict, handler=json.dumps, default={})
        ).parse(request.body)
        if error is None:
            if not has_host_perm(request.user, form.host_ids):
                return json_response(error='无权访问主机，请联系管理员')
            token, rds = uuid.uuid4().hex, get_redis_connection()
            form.host_ids.sort()
            if form.template_id:
                template = ExecTemplate.objects.filter(pk=form.template_id).first()
                if not template or template.body != form.command:
                    form.template_id = None

            ExecHistory.objects.create(
                user=request.user,
                digest=token,
                interpreter=form.interpreter,
                template_id=form.template_id,
                command=form.command,
                host_ids=json.dumps(form.host_ids),
                params=form.params
            )
            return json_response(token)
        return json_response(error=error)

    @auth('exec.task.do')
    def patch(self, request):
        form, error = JsonParser(
            Argument('token', help='参数错误'),
            Argument('cols', type=int, required=False),
            Argument('rows', type=int, required=False)
        ).parse(request.body)
        if error is None:
            term = None
            if form.cols and form.rows:
                term = {'width': form.cols, 'height': form.rows}
            rds = get_redis_connection()
            task = ExecHistory.objects.get(digest=form.token)
            for host in Host.objects.filter(id__in=json.loads(task.host_ids)):
                data = dict(
                    key=host.id,
                    name=host.name,
                    token=task.digest,
                    interpreter=task.interpreter,
                    hostname=host.hostname,
                    port=host.port,
                    username=host.username,
                    command=task.command,
                    pkey=host.private_key,
                    params=json.loads(task.params),
                    term=term
                )
                rds.rpush(settings.EXEC_WORKER_KEY, json.dumps(data))
        return json_response(error=error)


class TestPlanView(View):
    """测试计划管理API"""
    
    @auth('exec.task.do')  # 使用已有的执行权限
    def get(self, request, plan_id=None):
        """获取测试计划列表或单个测试计划"""
        # 检查是否需要替换变量
        replace_variables = request.GET.get('replace_variables', 'false').lower() == 'true'
        
        if plan_id:
            # 获取单个测试计划
            try:
                plan = TestPlan.objects.get(id=int(plan_id))
                return json_response({'data': plan.to_dict(replace_variables=replace_variables)})
            except TestPlan.DoesNotExist:
                return json_response(error='测试计划不存在')
        else:
            # 获取所有测试计划 - 包装在data字段中
            plans = TestPlan.objects.all()
            return json_response({'data': [plan.to_dict(replace_variables=replace_variables) for plan in plans]})
    
    @auth('exec.task.do')
    def post(self, request):
        """创建新的测试计划"""
        try:
            data = json.loads(request.body)
            
            # 生成新的ID
            new_id = max([plan.id for plan in TestPlan.objects.all()], default=0) + 1
            
            new_plan = TestPlan(
                name=data.get('name', ''),
                description=data.get('description', ''),
                category=data.get('category', ''),
                commands=json.dumps(data.get('commands', [])),
                files=json.dumps(data.get('files', [])),  # 新增文件关联
                steps=json.dumps(data.get('steps', [])),  # 添加新的steps字段
                step_interval=data.get('step_interval', 0),  # 步骤间隔
                file_path_strict=data.get('file_path_strict', False),  # 严格文件路径模式
                variables=json.dumps(data.get('variables', [])),  # 变量系统
                created_by_id=1,  # 暂时使用默认用户ID
                created_at=human_datetime(),
                updated_at=human_datetime()
            )
            
            new_plan.save()
            
            return json_response({
                'message': '测试计划创建成功',
                'plan': new_plan.to_dict()
            })
            
        except Exception as e:
            return json_response(error=f'创建失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, plan_id):
        """更新测试计划"""
        try:
            data = json.loads(request.body)
            plan_id = int(plan_id)
            
            print(f"[DEBUG] PUT test-plan {plan_id}, data: {data}")
            
            # 查找要更新的测试计划
            try:
                plan = TestPlan.objects.get(id=plan_id)
            except TestPlan.DoesNotExist:
                return json_response(error='测试计划不存在')
            
            # 更新测试计划
            plan.name = data.get('name', plan.name)
            plan.description = data.get('description', plan.description)
            plan.category = data.get('category', plan.category)
            plan.commands = json.dumps(data.get('commands', json.loads(plan.commands)))
            plan.files = json.dumps(data.get('files', json.loads(plan.files)))
            plan.steps = json.dumps(data.get('steps', json.loads(plan.steps)))
            plan.step_interval = data.get('step_interval', plan.step_interval)
            plan.file_path_strict = data.get('file_path_strict', plan.file_path_strict)
            plan.variables = json.dumps(data.get('variables', json.loads(plan.variables)))
            plan.updated_by_id = 1  # 暂时使用默认用户ID
            plan.updated_at = human_datetime()
            
            print(f"[DEBUG] Saving plan commands: {plan.commands}")
            print(f"[DEBUG] Saving plan steps: {plan.steps}")
            
            plan.save()
            
            return json_response({
                'message': '测试计划更新成功',
                'plan': plan.to_dict()
            })
            
        except Exception as e:
            return json_response(error=f'更新失败: {str(e)}')
    
    @auth('exec.task.do')
    def delete(self, request, plan_id):
        """删除测试计划"""
        try:
            plan_id = int(plan_id)
            
            # 查找要删除的测试计划
            try:
                plan = TestPlan.objects.get(id=plan_id)
                # 删除测试计划
                plan.delete()
            except TestPlan.DoesNotExist:
                return json_response(error='测试计划不存在')
            
            return json_response({
                'message': '测试计划删除成功'
            })
            
        except Exception as e:
            return json_response(error=f'删除失败: {str(e)}')


class TestPlanImportExportView(View):
    """测试计划导入导出API"""
    
    @auth('exec.task.do')
    def get(self, request):
        """导出测试计划"""
        try:
            # 获取要导出的计划ID列表
            plan_ids = request.GET.get('plan_ids', '')
            export_all = request.GET.get('export_all', 'false').lower() == 'true'
            include_files = request.GET.get('include_files', 'false').lower() == 'true'
            
            if export_all:
                # 导出所有测试计划
                plans = TestPlan.objects.all()
            elif plan_ids:
                # 导出指定的测试计划
                id_list = [int(x.strip()) for x in plan_ids.split(',') if x.strip().isdigit()]
                plans = TestPlan.objects.filter(id__in=id_list)
            else:
                return json_response(error='请指定要导出的测试计划')
            
            if include_files:
                # 压缩包模式：包含关联文件
                return self._export_with_files(plans)
            else:
                # JSON模式：仅导出配置
                return self._export_json_only(plans)
            
        except Exception as e:
            return json_response(error=f'导出失败: {str(e)}')
    
    def _export_json_only(self, plans):
        """仅导出JSON配置文件"""
        # 构建导出数据
        export_data = {
            'version': '1.0',
            'export_time': human_datetime(),
            'export_count': len(plans),
            'plans': []
        }
        
        for plan in plans:
            try:
                plan_data = plan.to_dict()
                # 移除不需要导出的字段
                plan_data.pop('id', None)
                plan_data.pop('created_by_id', None)
                plan_data.pop('updated_by_id', None)
                
                # 确保variables字段存在且有效
                if 'variables' not in plan_data or plan_data['variables'] is None:
                    plan_data['variables'] = []
                
                export_data['plans'].append(plan_data)
                print(f"[DEBUG] 导出计划 {plan.name}: variables={plan_data.get('variables', [])}")
            except Exception as e:
                print(f"[ERROR] 导出计划 {plan.name} 失败: {str(e)}")
                return json_response(error=f'导出计划"{plan.name}"失败: {str(e)}')
        
        from django.http import JsonResponse
        
        # 生成文件名
        timestamp = human_datetime().replace(" ", "_").replace(":", "-")
        if len(plans) == 1:
            # 单个测试计划导出，使用计划名称
            plan_name = plans[0].name
            # 移除文件名中的特殊字符，保留中文、英文、数字、下划线、连字符
            import re
            safe_name = re.sub(r'[^\w\u4e00-\u9fa5-]', '_', plan_name).strip('_')
            if not safe_name:
                safe_name = 'test_plan'
            filename = f'{safe_name}_{timestamp}.json'
        else:
            # 多个测试计划导出
            filename = f'test_plans_export_{timestamp}.json'
        
        response = JsonResponse(export_data, json_dumps_params={'ensure_ascii': False, 'indent': 2})
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
    
    def _export_with_files(self, plans):
        """导出包含关联文件的压缩包"""
        import zipfile
        import tempfile
        import os
        import shutil
        from django.conf import settings
        from django.http import HttpResponse
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        zip_path = os.path.join(temp_dir, 'export.zip')
        
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 收集所有文件信息
                all_files = set()  # 用于去重
                file_mapping = {}  # 文件路径映射
                
                # 构建导出数据
                export_data = {
                    'version': '2.0',  # 新版本支持文件
                    'export_time': human_datetime(),
                    'export_count': len(plans),
                    'file_mapping': {},  # 文件路径映射表
                    'plans': []
                }
                
                files_dir = os.path.join(settings.BASE_DIR, 'files')
                
                for plan_idx, plan in enumerate(plans):
                    try:
                        plan_data = plan.to_dict()
                        # 移除不需要导出的字段
                        plan_data.pop('id', None)
                        plan_data.pop('created_by_id', None)
                        plan_data.pop('updated_by_id', None)
                        
                        # 确保variables字段存在且有效
                        if 'variables' not in plan_data or plan_data['variables'] is None:
                            plan_data['variables'] = []
                        
                        # 处理步骤中的文件
                        steps = json.loads(plan_data.get('steps', '[]'))
                        for step_idx, step in enumerate(steps):
                            step_files = step.get('files', [])
                            for file_idx, file_info in enumerate(step_files):
                                file_path = file_info.get('path', '')
                                if file_path and file_path not in all_files:
                                    # 检查文件是否存在
                                    source_file = os.path.join(files_dir, file_path)
                                    if os.path.exists(source_file):
                                        # 生成压缩包内的文件路径
                                        file_ext = os.path.splitext(file_path)[1]
                                        archive_path = f"files/plan_{plan_idx}_step_{step_idx}_{file_info.get('name', f'file_{file_idx}')}{file_ext}"
                                        
                                        # 确保文件名唯一
                                        counter = 1
                                        original_archive_path = archive_path
                                        while archive_path in file_mapping.values():
                                            name_part, ext_part = os.path.splitext(original_archive_path)
                                            archive_path = f"{name_part}_{counter}{ext_part}"
                                            counter += 1
                                        
                                        # 添加文件到压缩包
                                        zipf.write(source_file, archive_path)
                                        
                                        # 记录映射关系
                                        file_mapping[file_path] = archive_path
                                        all_files.add(file_path)
                                        
                                        # 更新文件信息中的路径为压缩包内路径
                                        file_info['archive_path'] = archive_path
                                        file_info['original_path'] = file_path
                                        
                                        print(f"[DEBUG] 添加文件到压缩包: {file_path} -> {archive_path}")
                        
                        # 更新计划数据
                        plan_data['steps'] = json.dumps(steps)
                        export_data['plans'].append(plan_data)
                        
                    except Exception as e:
                        print(f"[ERROR] 处理计划 {plan.name} 失败: {str(e)}")
                        continue
                
                # 保存文件映射表
                export_data['file_mapping'] = file_mapping
                
                # 将配置文件添加到压缩包
                import json as json_module
                config_content = json_module.dumps(export_data, ensure_ascii=False, indent=2)
                zipf.writestr('test_plan.json', config_content.encode('utf-8'))
                
                # 添加清单文件
                manifest = {
                    'format_version': '2.0',
                    'exported_at': human_datetime(),
                    'plan_count': len(plans),
                    'file_count': len(all_files),
                    'structure': {
                        'test_plan.json': '测试计划配置文件',
                        'files/': '关联文件目录',
                        'manifest.json': '压缩包清单文件'
                    }
                }
                manifest_content = json_module.dumps(manifest, ensure_ascii=False, indent=2)
                zipf.writestr('manifest.json', manifest_content.encode('utf-8'))
                
                print(f"[DEBUG] 压缩包创建完成，包含 {len(plans)} 个计划和 {len(all_files)} 个文件")
            
            # 生成文件名
            timestamp = human_datetime().replace(" ", "_").replace(":", "-")
            if len(plans) == 1:
                plan_name = plans[0].name
                import re
                safe_name = re.sub(r'[^\w\u4e00-\u9fa5-]', '_', plan_name).strip('_')
                if not safe_name:
                    safe_name = 'test_plan'
                filename = f'{safe_name}_with_files_{timestamp}.zip'
            else:
                filename = f'test_plans_with_files_{timestamp}.zip'
            
            # 读取压缩包内容并返回
            with open(zip_path, 'rb') as f:
                zip_content = f.read()
            
            response = HttpResponse(zip_content, content_type='application/zip')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            response['Content-Length'] = len(zip_content)
            
            return response
            
        except Exception as e:
            print(f"[ERROR] 创建压缩包失败: {str(e)}")
            import traceback
            print(f"[ERROR] Traceback: {traceback.format_exc()}")
            return json_response(error=f'创建压缩包失败: {str(e)}')
        finally:
            # 清理临时文件
            try:
                shutil.rmtree(temp_dir)
            except:
                pass
    
    @auth('exec.task.do')
    def post(self, request):
        """导入测试计划"""
        try:
            import_file = request.FILES.get('file')
            if not import_file:
                return json_response(error='请选择要导入的文件')
            
            # 检查文件类型
            if import_file.name.endswith('.zip'):
                # 压缩包模式
                return self._import_from_zip(request, import_file)
            elif import_file.name.endswith('.json'):
                # JSON模式
                return self._import_from_json(request, import_file)
            else:
                return json_response(error='只支持JSON或ZIP格式的文件')
            
        except Exception as e:
            return json_response(error=f'导入失败: {str(e)}')
    
    def _import_from_json(self, request, import_file):
        """从JSON文件导入测试计划"""
        # 读取文件内容
        try:
            file_content = import_file.read().decode('utf-8')
            import_data = json.loads(file_content)
        except json.JSONDecodeError:
            return json_response(error='文件格式错误，请选择有效的JSON文件')
        except UnicodeDecodeError:
            return json_response(error='文件编码错误，请使用UTF-8编码的文件')
        
        # 验证导入数据格式
        if not isinstance(import_data, dict) or 'plans' not in import_data:
            return json_response(error='导入文件格式不正确，缺少plans字段')
        
        plans_data = import_data.get('plans', [])
        if not isinstance(plans_data, list):
            return json_response(error='导入文件格式不正确，plans字段应为数组')
        
        if len(plans_data) == 0:
            return json_response(error='导入文件中没有测试计划数据')
        
        return self._process_import_plans(request, plans_data, {})
    
    def _import_from_zip(self, request, import_file):
        """从压缩包导入测试计划和文件"""
        import zipfile
        import tempfile
        import os
        import shutil
        from django.conf import settings
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 保存上传的压缩包
            zip_path = os.path.join(temp_dir, 'import.zip')
            with open(zip_path, 'wb') as f:
                for chunk in import_file.chunks():
                    f.write(chunk)
            
            # 解压文件
            extract_dir = os.path.join(temp_dir, 'extracted')
            os.makedirs(extract_dir)
            
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                zipf.extractall(extract_dir)
            
            # 读取配置文件
            config_path = os.path.join(extract_dir, 'test_plan.json')
            if not os.path.exists(config_path):
                return json_response(error='压缩包中缺少 test_plan.json 配置文件')
            
            with open(config_path, 'r', encoding='utf-8') as f:
                import_data = json.loads(f.read())
            
            # 验证导入数据格式
            if not isinstance(import_data, dict) or 'plans' not in import_data:
                return json_response(error='配置文件格式不正确，缺少plans字段')
            
            plans_data = import_data.get('plans', [])
            file_mapping = import_data.get('file_mapping', {})
            
            if len(plans_data) == 0:
                return json_response(error='配置文件中没有测试计划数据')
            
            # 处理文件导入
            files_imported = 0
            files_dir = os.path.join(settings.BASE_DIR, 'files')
            import_timestamp = human_datetime().replace(" ", "_").replace(":", "-")
            import_folder = f'imported_{import_timestamp}'
            target_import_dir = os.path.join(files_dir, import_folder)
            
            # 确保导入目录存在
            os.makedirs(target_import_dir, exist_ok=True)
            
            # 复制压缩包中的文件到文件管理器
            files_source_dir = os.path.join(extract_dir, 'files')
            if os.path.exists(files_source_dir):
                for root, dirs, files in os.walk(files_source_dir):
                    for file in files:
                        source_path = os.path.join(root, file)
                        # 计算相对路径
                        rel_path = os.path.relpath(source_path, files_source_dir)
                        target_path = os.path.join(target_import_dir, rel_path)
                        
                        # 确保目标目录存在
                        os.makedirs(os.path.dirname(target_path), exist_ok=True)
                        
                        # 复制文件
                        shutil.copy2(source_path, target_path)
                        files_imported += 1
                        
                        print(f"[DEBUG] 导入文件: {rel_path} -> {target_path}")
            
            # 更新计划数据中的文件路径
            for plan_data in plans_data:
                steps = json.loads(plan_data.get('steps', '[]'))
                for step in steps:
                    step_files = step.get('files', [])
                    for file_info in step_files:
                        archive_path = file_info.get('archive_path', '')
                        if archive_path:
                            # 将archive_path转换为新的文件管理器路径
                            rel_path = archive_path.replace('files/', '', 1)
                            new_path = os.path.join(import_folder, rel_path)
                            file_info['path'] = new_path
                            # 清理临时字段
                            file_info.pop('archive_path', None)
                            file_info.pop('original_path', None)
                            
                            print(f"[DEBUG] 更新文件路径: {archive_path} -> {new_path}")
                
                # 更新步骤数据
                plan_data['steps'] = json.dumps(steps)
            
            # 处理测试计划导入
            result = self._process_import_plans(request, plans_data, {})
            
            # 更新结果信息，包含文件导入统计
            if isinstance(result.content, bytes):
                import json as json_module
                result_data = json_module.loads(result.content.decode('utf-8'))
                if 'details' in result_data:
                    result_data['details']['files_imported'] = files_imported
                    result_data['details']['import_folder'] = import_folder
                
                # 更新消息
                if files_imported > 0:
                    result_data['message'] += f'，导入文件 {files_imported} 个到 {import_folder} 目录'
                
                # 重新构造响应
                return json_response(result_data)
            
            return result
            
        except zipfile.BadZipFile:
            return json_response(error='压缩包文件损坏或格式不正确')
        except Exception as e:
            print(f"[ERROR] 压缩包导入失败: {str(e)}")
            import traceback
            print(f"[ERROR] Traceback: {traceback.format_exc()}")
            return json_response(error=f'压缩包导入失败: {str(e)}')
        finally:
            # 清理临时文件
            try:
                shutil.rmtree(temp_dir)
            except:
                pass
    
    def _process_import_plans(self, request, plans_data, file_mapping):
        """处理测试计划导入的通用逻辑"""
        # 导入选项
        skip_existing = request.POST.get('skip_existing', 'false').lower() == 'true'
        update_existing = request.POST.get('update_existing', 'false').lower() == 'true'
        
        imported_count = 0
        skipped_count = 0
        updated_count = 0
        error_count = 0
        errors = []
        
        for i, plan_data in enumerate(plans_data):
            try:
                # 验证必要字段
                if not plan_data.get('name'):
                    errors.append(f'第{i+1}个计划缺少名称字段')
                    error_count += 1
                    continue
                
                # 检查是否已存在同名计划
                existing_plan = TestPlan.objects.filter(name=plan_data['name']).first()
                
                if existing_plan:
                    if skip_existing:
                        skipped_count += 1
                        continue
                    elif update_existing:
                        # 更新现有计划
                        existing_plan.description = plan_data.get('description', existing_plan.description)
                        existing_plan.category = plan_data.get('category', existing_plan.category)
                        existing_plan.commands = json.dumps(plan_data.get('commands', json.loads(existing_plan.commands)))
                        existing_plan.files = json.dumps(plan_data.get('files', json.loads(existing_plan.files)))
                        existing_plan.steps = json.dumps(plan_data.get('steps', json.loads(existing_plan.steps)))
                        existing_plan.step_interval = plan_data.get('step_interval', existing_plan.step_interval)
                        existing_plan.file_path_strict = plan_data.get('file_path_strict', existing_plan.file_path_strict)
                        existing_plan.variables = json.dumps(plan_data.get('variables', json.loads(existing_plan.variables)))
                        existing_plan.updated_at = human_datetime()
                        existing_plan.updated_by_id = 1
                        existing_plan.save()
                        updated_count += 1
                    else:
                        errors.append(f'测试计划"{plan_data["name"]}"已存在')
                        error_count += 1
                        continue
                else:
                    # 创建新计划
                    new_plan = TestPlan(
                        name=plan_data['name'],
                        description=plan_data.get('description', ''),
                        category=plan_data.get('category', ''),
                        commands=json.dumps(plan_data.get('commands', [])),
                        files=json.dumps(plan_data.get('files', [])),
                        steps=json.dumps(plan_data.get('steps', [])),
                        step_interval=plan_data.get('step_interval', 0),
                        file_path_strict=plan_data.get('file_path_strict', False),
                        variables=json.dumps(plan_data.get('variables', [])),
                        created_by_id=1,
                        updated_by_id=1,
                        created_at=human_datetime(),
                        updated_at=human_datetime()
                    )
                    new_plan.save()
                    imported_count += 1
                    
            except Exception as e:
                errors.append(f'第{i+1}个计划导入失败: {str(e)}')
                error_count += 1
        
        # 返回导入结果
        result_message = f'导入完成: 成功导入{imported_count}个'
        if updated_count > 0:
            result_message += f', 更新{updated_count}个'
        if skipped_count > 0:
            result_message += f', 跳过{skipped_count}个'
        if error_count > 0:
            result_message += f', 失败{error_count}个'
        
        return json_response({
            'message': result_message,
            'details': {
                'imported': imported_count,
                'updated': updated_count,
                'skipped': skipped_count,
                'errors': error_count,
                'error_messages': errors[:10]  # 只返回前10个错误信息
            }
        })


class TestPlanExecutionView(View):
    """测试计划执行API"""
    
    @auth('exec.task.do')
    def get(self, request, execution_id=None):
        """获取测试计划执行记录"""
        print(f"[DEBUG] GET test-plan-executions, execution_id: {execution_id}")
        print(f"[DEBUG] Current executions: {[exec.token for exec in TestPlanExecution.objects.all()]}")
        
        if execution_id:
            # 支持通过token查询
            if isinstance(execution_id, str) and len(execution_id) == 32:
                # 这是一个token
                print(f"[DEBUG] Looking for token: {execution_id}")
                try:
                    execution = TestPlanExecution.objects.get(token=execution_id)
                except TestPlanExecution.DoesNotExist:
                    execution = None
            else:
                # 这是一个ID
                try:
                    print(f"[DEBUG] Looking for ID: {execution_id}")
                    execution = TestPlanExecution.objects.get(id=int(execution_id))
                except (TestPlanExecution.DoesNotExist, ValueError):
                    # 可能是token格式
                    print(f"[DEBUG] Fallback to token: {execution_id}")
                    try:
                        execution = TestPlanExecution.objects.get(token=execution_id)
                    except TestPlanExecution.DoesNotExist:
                        execution = None
            
            print(f"[DEBUG] Found execution: {execution is not None}")
            
            if not execution:
                return json_response(error='执行记录不存在')
            
            # 获取该执行的步骤结果
            steps = list(TestPlanStepResult.objects.filter(execution_id=execution.id))
            
            # 获取测试计划详情
            try:
                plan = TestPlan.objects.get(id=execution.plan_id)
            except TestPlan.DoesNotExist:
                plan = None
            
            # 准备返回数据
            execution_data = execution.to_dict()
            execution_data['steps'] = [step.to_dict() for step in steps]
            execution_data['plan'] = plan.to_dict() if plan else None
            
            print(f"[DEBUG] Returning execution data with plan: {plan is not None}")
            return json_response({'data': execution_data})
        else:
            # 获取所有执行记录
            return json_response({'data': [exec.to_dict() for exec in TestPlanExecution.objects.all()]})
    
    @auth('exec.task.do')
    def post(self, request):
        """开始执行测试计划"""
        try:
            data = json.loads(request.body)
            print(f"[DEBUG] POST test-plan-executions, data: {data}")
            
            plan_id = data.get('plan_id')
            host_ids = data.get('host_ids', [])
            single_step = data.get('single_step')  # 单步执行的步骤索引
            step_data = data.get('step_data')      # 单步执行的步骤数据
            
            print(f"[DEBUG] plan_id: {plan_id}, host_ids: {host_ids}, single_step: {single_step}")
            
            if not plan_id:
                return json_response(error='请指定测试计划ID')
            if not host_ids:
                return json_response(error='请选择执行主机')
            
            # 查找测试计划
            try:
                plan = TestPlan.objects.get(id=int(plan_id))
            except TestPlan.DoesNotExist:
                return json_response(error='测试计划不存在')
            
            from apps.host.models import Host
            # 验证主机权限
            from apps.account.utils import has_host_perm
            if not has_host_perm(request.user, host_ids):
                return json_response(error='无权访问主机，请联系管理员')
            
            # 创建执行记录
            token = uuid.uuid4().hex
            
            # 如果是单步执行，修改计划名称
            plan_name = plan.name
            if single_step is not None:
                step_label = step_data.get('label', f'步骤 {single_step + 1}') if step_data else f'步骤 {single_step + 1}'
                plan_name = f"{plan.name} - {step_label} (单步执行)"
            
            execution = TestPlanExecution(
                token=token,
                plan_id=plan_id,
                plan_name=plan_name,
                host_ids=json.dumps(host_ids),
                user_id=1,  # 暂时使用默认用户ID
                executor_id=1,  # 暂时使用默认用户ID
                executor_name='系统管理员',
                status='running',
                start_time=human_datetime(),
                end_time=None,
                total_steps=1 if single_step is not None else len(json.loads(plan.files)) + len(json.loads(plan.commands)),
                completed_steps=0
            )
            
            execution.save()
            print(f"[DEBUG] Created execution with token: {token}")
            
            # 异步执行测试计划
            from threading import Thread
            if single_step is not None:
                # 单步执行
                Thread(target=self._execute_single_step, args=(execution, plan, host_ids, single_step, step_data)).start()
            else:
                # 完整执行
                Thread(target=self._execute_test_plan, args=(execution, plan, host_ids)).start()
            
            return json_response({
                'message': '测试计划执行已开始',
                'execution_id': execution.id,
                'token': token
            })
            
        except Exception as e:
            return json_response(error=f'执行失败: {str(e)}')
    
    def _execute_single_step(self, execution, plan, host_ids, step_index, step_data):
        """执行单个测试步骤"""
        from apps.host.models import Host
        import time
        
        token = execution.token
        execution_id = execution.id
        rds = get_redis_connection()
        
        # 创建日志文件
        self.log_content = []
        
        # 初始化步骤日志文件夹
        self._init_step_logs_folder(execution)
        
        print(f"[DEBUG] Starting single step execution: {step_data.get('label', f'步骤 {step_index + 1}')}")
        print(f"[DEBUG] Token: {token}, Host IDs: {host_ids}")
        
        # 发布开始消息
        step_label = step_data.get('label', f'步骤 {step_index + 1}')
        start_msg = f'开始单步执行: {step_label}'
        self.log_content.append(f'[{human_datetime()}] {start_msg}')
        rds.publish(token, json.dumps({
            'type': 'info',
            'message': start_msg
        }))
        
        try:
            all_success = True
            
            for host_id in host_ids:
                host = Host.objects.get(pk=host_id)
                
                connect_msg = f'正在连接主机: {host.name} ({host.hostname})'
                self.log_content.append(f'[{human_datetime()}] {connect_msg}')
                rds.publish(token, json.dumps({
                    'type': 'info', 
                    'message': connect_msg
                }))
                
                step_msg = f'📋 执行步骤: {step_label}'
                self.log_content.append(f'[{human_datetime()}] {step_msg}')
                rds.publish(token, json.dumps({
                    'type': 'info',
                    'message': step_msg
                }))
                
                # 记录步骤是否成功
                step_success = True
                
                # 注意：单步执行时不会处理文件传输，因为文件可能已经不存在了
                # 显示提醒信息
                if step_data.get('files'):
                    warning_msg = f'⚠️ 注意：此步骤包含 {len(step_data["files"])} 个文件，但单步执行不会传输文件'
                    self.log_content.append(f'[{human_datetime()}] {warning_msg}')
                    rds.publish(token, json.dumps({
                        'type': 'warning',
                        'message': warning_msg
                    }))
                
                # 执行命令
                if step_data.get('command'):
                    cmd_step_id = self._create_step_result(execution_id, 'command', 
                                                         step_label, step_data['command'])
                    
                    success = self._execute_command(rds, token, host, step_data, cmd_step_id, plan)
                    if not success:
                        step_success = False
                        all_success = False
                
                # 发布步骤完成状态
                if step_success:
                    rds.publish(token, json.dumps({
                        'type': 'success',
                        'message': f'✅ 步骤在主机 {host.name} 上执行成功'
                    }))
                else:
                    rds.publish(token, json.dumps({
                        'type': 'error',
                        'message': f'❌ 步骤在主机 {host.name} 上执行失败'
                    }))
            
            # 更新执行状态
            execution.status = 'success' if all_success else 'failed'
            execution.end_time = human_datetime()
            execution.completed_steps = 1
            execution.save()
            
            # 发布完成消息
            final_status = '成功' if all_success else '失败'
            final_msg = f'单步执行完成: {final_status}'
            self.log_content.append(f'[{human_datetime()}] {final_msg}')
            rds.publish(token, json.dumps({
                'type': 'success' if all_success else 'error',
                'message': final_msg
            }))
            
            # 保存执行日志
            self._save_execution_log(execution, self.log_content)
            
        except Exception as e:
            print(f"[ERROR] Single step execution failed: {str(e)}")
            execution.status = 'failed'
            execution.end_time = human_datetime()
            execution.save()
            
            error_msg = f'单步执行出错: {str(e)}'
            self.log_content.append(f'[{human_datetime()}] {error_msg}')
            rds.publish(token, json.dumps({
                'type': 'error',
                'message': error_msg
            }))
            
            # 保存执行日志
            self._save_execution_log(execution, self.log_content)
    
    def _execute_test_plan(self, execution, plan, host_ids):
        """执行测试计划的主要逻辑"""
        from apps.host.models import Host
        import time
        
        token = execution.token
        execution_id = execution.id
        rds = get_redis_connection()
        
        # 创建日志文件 - 使用实例变量，方便其他方法访问
        self.log_content = []
        
        # 初始化步骤日志文件夹
        self._init_step_logs_folder(execution)
        
        print(f"[DEBUG] Starting test plan execution: {plan.name}")
        print(f"[DEBUG] Token: {token}, Host IDs: {host_ids}")
        
        # 发布开始消息
        start_msg = f'开始执行测试计划: {plan.name}'
        self.log_content.append(f'[{human_datetime()}] {start_msg}')
        rds.publish(token, json.dumps({
            'type': 'info',
            'message': start_msg
        }))
        
        try:
            for host_id in host_ids:
                host = Host.objects.get(pk=host_id)
                
                connect_msg = f'正在连接主机: {host.name} ({host.hostname})'
                self.log_content.append(f'[{human_datetime()}] {connect_msg}')
                rds.publish(token, json.dumps({
                    'type': 'info', 
                    'message': connect_msg
                }))
                
                # SSH连接检测
                ssh_check_success = self._check_ssh_connection(rds, token, host)
                if not ssh_check_success:
                    # SSH连接失败，跳过该主机
                    error_msg = f'❌ 主机 {host.name} SSH连接失败，跳过该主机的执行'
                    self.log_content.append(f'[{human_datetime()}] {error_msg}')
                    rds.publish(token, json.dumps({
                        'type': 'error',
                        'message': error_msg
                    }))
                    continue
                
                # 按步骤执行（新的步骤化结构）
                commands = json.loads(plan.commands)
                print(f"[DEBUG] Loaded plan.commands: {commands}")
                
                # 自动修正文件的targetPath，确保与步骤的workPath一致
                commands_updated = False
                for step in commands:
                    if step.get('files') and step.get('workPath'):
                        work_path = step['workPath']
                        for file_info in step['files']:
                            old_target = file_info.get('targetPath', '')
                            # 如果targetPath是/tmp而workPath不是/tmp，或者targetPath为空，则更新
                            if (old_target.startswith('/tmp/') and work_path != '/tmp') or not old_target:
                                new_target = f"{work_path}/{file_info['name']}"
                                print(f"[DEBUG] Auto-correcting targetPath: {old_target} -> {new_target}")
                                file_info['targetPath'] = new_target
                                commands_updated = True
                
                # 如果有更新，保存回数据库
                if commands_updated:
                    plan.commands = json.dumps(commands)
                    plan.save()
                    print(f"[DEBUG] Auto-corrected plan commands saved")
                
                if commands:
                    rds.publish(token, json.dumps({
                        'type': 'info',
                        'message': f'🚀 开始执行 {len(commands)} 个步骤'
                    }))
                    
                    for step_index, step in enumerate(commands):
                        if not step.get('enabled', True):
                            continue
                        
                        step_label = step.get('label', f'步骤{step_index + 1}')
                        
                        print(f"[DEBUG] Processing step {step_index+1}: {step}")
                        print(f"[DEBUG] Step workPath: {step.get('workPath')}")
                        print(f"[DEBUG] Step files: {step.get('files', [])}")
                        
                        step_msg = f'📋 步骤 {step_index + 1}: {step_label}'
                        self.log_content.append(f'[{human_datetime()}] {step_msg}')
                        rds.publish(token, json.dumps({
                            'type': 'info',
                            'message': step_msg
                        }))
                        
                        # 记录步骤是否成功
                        step_success = True
                        
                        # 先处理该步骤的文件传输
                        step_files = step.get('files', [])
                        if step_files:
                            rds.publish(token, json.dumps({
                                'type': 'info',
                                'message': f'  📁 传输 {len(step_files)} 个文件...'
                            }))
                            
                            for file_info in step_files:
                                file_step_id = self._create_step_result(execution_id, 'file_transfer', 
                                                               f'{step_label} - {file_info["name"]}', None, 
                                                               file_info.get('path', ''))
                                
                                # 传递步骤的执行路径作为默认文件传输目标
                                step_work_path = step.get('workPath', '/tmp')
                                print(f"[DEBUG] Step {step_index+1} workPath: {step_work_path}")
                                print(f"[DEBUG] File info: {file_info}")
                                
                                # 添加调试日志
                                debug_msg = f'    🔍 调试信息: 步骤工作路径={step_work_path}, 文件目标路径={file_info.get("targetPath", "未设置")}'
                                self.log_content.append(f'[{human_datetime()}] {debug_msg}')
                                rds.publish(token, json.dumps({
                                    'type': 'info',
                                    'message': debug_msg
                                }))
                                
                                file_result = self._transfer_file(rds, token, host, file_info, file_step_id, step_work_path, plan)
                                if file_result == 'skip':
                                    # 文件跳过，不影响步骤成功状态
                                    skip_msg = f'  ⚠️ 文件 {file_info["name"]} 被跳过，继续执行'
                                    rds.publish(token, json.dumps({
                                        'type': 'info',
                                        'message': skip_msg
                                    }))
                                elif not file_result:
                                    step_success = False
                                    rds.publish(token, json.dumps({
                                        'type': 'warning',
                                        'message': f'  ⚠️ 文件传输失败，但继续执行后续步骤'
                                    }))
                        
                        # 再执行该步骤的命令
                        if step.get('command'):
                            cmd_step_id = self._create_step_result(execution_id, 'command', 
                                                                 step_label, step['command'])
                            
                            success = self._execute_command(rds, token, host, step, cmd_step_id, plan)
                            if not success:
                                step_success = False
                                rds.publish(token, json.dumps({
                                    'type': 'warning',
                                    'message': f'  ⚠️ 命令执行失败，但继续执行后续步骤'
                                }))
                        
                        # 发布步骤完成状态
                        if step_success:
                            rds.publish(token, json.dumps({
                                'type': 'success',
                                'message': f'  ✅ 步骤 {step_index + 1} 完成'
                            }))
                        else:
                            rds.publish(token, json.dumps({
                                'type': 'warning',
                                'message': f'  ⚠️ 步骤 {step_index + 1} 完成（有错误）'
                            }))
                        
                        # 在步骤之间添加延迟（如果配置了步骤间隔且不是最后一个步骤）
                        if plan.step_interval > 0 and step_index < len(commands) - 1:
                            # 如果步骤失败，可以选择缩短等待时间或跳过等待
                            actual_interval = plan.step_interval
                            if not step_success:
                                # 步骤失败时，等待时间减半，最少1秒
                                actual_interval = max(1, plan.step_interval // 2)
                                interval_msg = f'  ⏱️ 步骤失败，缩短等待时间至 {actual_interval} 秒后执行下一步骤...'
                            else:
                                interval_msg = f'  ⏱️ 等待 {actual_interval} 秒后执行下一步骤...'
                            
                            self.log_content.append(f'[{human_datetime()}] {interval_msg}')
                            rds.publish(token, json.dumps({
                                'type': 'info',
                                'message': interval_msg
                            }))
                            time.sleep(actual_interval)
                
            # 执行完成（可能有部分步骤失败，但整体流程完成）
            execution.status = 'completed'
            execution.end_time = human_datetime()
            
            end_msg = '🎉 测试计划执行完成! (部分步骤可能有错误，请查看详细日志)'
            self.log_content.append(f'[{human_datetime()}] {end_msg}')
            
            # 保存日志文件到文件管理系统
            self._save_execution_log(execution, self.log_content)
            
            rds.publish(token, json.dumps({
                'type': 'success',
                'message': end_msg
            }))
            
        except Exception as e:
            print(f"[DEBUG] Test plan execution failed: {str(e)}")
            import traceback
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
            
            execution.status = 'failed'
            execution.end_time = human_datetime()
            
            rds.publish(token, json.dumps({
                'type': 'error',
                'message': f'❌ 测试计划执行失败: {str(e)}'
            }))
    
    def _create_step_result(self, execution_id, step_type, step_name, command=None, file_path=None):
        """创建步骤结果记录"""
        step = TestPlanStepResult(
            execution_id=execution_id,
            step_type=step_type,
            step_name=step_name,
            command=command,
            file_path=file_path,
            start_time=human_datetime(),
            end_time=None,
            exit_code=None,
            output='',
            status='running'
        )
        
        step.save()
        return step.id
    
    def _transfer_file(self, rds, token, host, file_info, step_id, work_path='/tmp', plan=None):
        """传输文件到目标主机"""
        try:
            file_name = file_info['name']
            file_path = file_info['path']
            target_path = file_info.get('targetPath', f'{work_path}/{file_name}')
            
            # 获取测试计划的变量并对目标路径进行变量替换
            variables = []
            if plan:
                try:
                    variables = json.loads(plan.variables) if hasattr(plan, 'variables') else []
                except:
                    variables = []
            
            # 对目标路径进行变量替换
            target_path = self._preprocess_command(target_path, variables)
            
            print(f"[DEBUG] Transferring file: {file_path} -> {target_path}")
            
            # 添加变量替换日志
            if variables:
                print(f"[DEBUG] Variables applied to target path: {len(variables)} variables")
            
            # 记录文件传输开始信息到主日志
            self.log_content.append(f'[{human_datetime()}] 开始文件传输: {file_name}')
            self.log_content.append(f'[{human_datetime()}] 目标主机: {host.name}')
            self.log_content.append(f'[{human_datetime()}] 源文件路径: {file_path}')
            self.log_content.append(f'[{human_datetime()}] 目标路径: {target_path}')
            
            transfer_msg = f'    📁 传输文件: {file_name} -> {target_path}'
            self.log_content.append(f'[{human_datetime()}] {transfer_msg}')
            rds.publish(token, json.dumps({
                'type': 'info', 
                'message': transfer_msg
            }))
            
            # 检查源文件是否存在
            import os
            from django.conf import settings
            source_file_path = os.path.join(settings.BASE_DIR, 'files', file_path)
            
            if not os.path.exists(source_file_path):
                error_msg = f'    ❌ 源文件不存在: {file_path}'
                self.log_content.append(f'[{human_datetime()}] {error_msg}')
                rds.publish(token, json.dumps({
                    'type': 'error',
                    'message': error_msg
                }))
                # 更新步骤结果状态为失败
                try:
                    step_result = TestPlanStepResult.objects.get(id=step_id)
                    step_result.status = 'failed'
                    step_result.end_time = human_datetime()
                    step_result.output = f'源文件不存在: {file_path}'
                    step_result.exit_code = 1
                    step_result.save()
                except TestPlanStepResult.DoesNotExist:
                    pass
                return False
            
            # 使用SSH传输文件
            with host.get_ssh() as ssh:
                # 检查目标目录是否存在，根据严格模式决定行为
                target_dir = os.path.dirname(target_path)
                if target_dir and target_dir != '/':
                    # 先检查目标目录是否存在
                    exit_code, output = ssh.exec_command_raw(f'test -d {target_dir}')
                    dir_exists = (exit_code == 0)
                    
                    # 根据测试计划的file_path_strict配置决定行为
                    file_path_strict = getattr(plan, 'file_path_strict', False) if plan else False
                    
                    if not dir_exists:
                        if file_path_strict:
                            # 严格模式：目标目录不存在时跳过传输
                            skip_msg = f'    ⚠️ 目标目录不存在，跳过传输: {target_dir} (严格路径模式)'
                            self.log_content.append(f'[{human_datetime()}] {skip_msg}')
                            rds.publish(token, json.dumps({
                                'type': 'warning',
                                'message': skip_msg
                            }))
                            # 更新步骤结果状态为跳过
                            try:
                                step_result = TestPlanStepResult.objects.get(id=step_id)
                                step_result.status = 'skipped'
                                step_result.end_time = human_datetime()
                                step_result.output = f'目标目录不存在，跳过传输: {target_dir} (严格路径模式)'
                                step_result.save()
                            except TestPlanStepResult.DoesNotExist:
                                pass
                            
                            # 返回特殊值 'skip' 表示跳过该文件
                            return 'skip'
                        else:
                            # 兼容模式：自动创建目标目录
                            ssh.exec_command_raw(f'mkdir -p {target_dir}')
                            self.log_content.append(f'[{human_datetime()}] 自动创建目标目录: {target_dir} (兼容模式)')
                    else:
                        self.log_content.append(f'[{human_datetime()}] 目标目录已存在: {target_dir}')
                
                # 传输文件
                ssh.put_file(source_file_path, target_path)
                self.log_content.append(f'[{human_datetime()}] 文件传输完成')
                
                # 验证文件是否成功传输
                exit_code, output = ssh.exec_command_raw(f'ls -la {target_path}')
                if exit_code == 0:
                    success_msg = f'    ✅ 文件传输成功: {file_name}'
                    self.log_content.append(f'[{human_datetime()}] {success_msg}')
                    self.log_content.append(f'[{human_datetime()}] 文件信息: {output.strip()}')
                    rds.publish(token, json.dumps({
                        'type': 'success',
                        'message': success_msg
                    }))
                    
                    # 更新步骤结果状态为成功
                    try:
                        step_result = TestPlanStepResult.objects.get(id=step_id)
                        step_result.status = 'success'
                        step_result.end_time = human_datetime()
                        step_result.output = f'文件传输成功: {file_name}\n{output.strip()}'
                        step_result.save()
                    except TestPlanStepResult.DoesNotExist:
                        pass
                    
                    return True
                else:
                    error_msg = f'    ❌ 文件传输验证失败: {file_name}'
                    self.log_content.append(f'[{human_datetime()}] {error_msg}')
                    rds.publish(token, json.dumps({
                        'type': 'error',
                        'message': error_msg
                    }))
                    
                    # 更新步骤结果状态为失败
                    try:
                        step_result = TestPlanStepResult.objects.get(id=step_id)
                        step_result.status = 'failed'
                        step_result.end_time = human_datetime()
                        step_result.output = f'文件传输验证失败: {file_name}'
                        step_result.exit_code = exit_code
                        step_result.save()
                    except TestPlanStepResult.DoesNotExist:
                        pass
                    
                    return False
                    
        except Exception as e:
            error_msg = f'    ❌ 文件传输异常: {file_name} - {str(e)}'
            self.log_content.append(f'[{human_datetime()}] 传输异常: {str(e)}')
            self.log_content.append(f'[{human_datetime()}] {error_msg}')
            rds.publish(token, json.dumps({
                'type': 'error',
                'message': error_msg
            }))
            
            # 更新步骤结果状态为异常
            try:
                step_result = TestPlanStepResult.objects.get(id=step_id)
                step_result.status = 'error'
                step_result.end_time = human_datetime()
                step_result.output = f'文件传输异常: {str(e)}'
                step_result.save()
            except TestPlanStepResult.DoesNotExist:
                pass
            
            return False
    
    def _execute_assertion(self, rds, token, host, step, step_result, main_output):
        """执行断言检查"""
        assertion_config = step.get('assertion', {})
        if not assertion_config or not assertion_config.get('enabled'):
            return True, "断言未启用"
        
        assertion_type = assertion_config.get('type', 'command')
        
        try:
            # 更新步骤结果的断言信息
            step_result.assertion_enabled = True
            step_result.assertion_type = assertion_type
            step_result.assertion_config = json.dumps(assertion_config)
            step_result.assertion_status = 'running'
            step_result.save()
            
            # 发布断言开始消息
            assertion_msg = f'    🔍 开始执行断言检查 ({assertion_type})'
            self.log_content.append(f'[{human_datetime()}] {assertion_msg}')
            rds.publish(token, json.dumps({
                'type': 'info',
                'message': assertion_msg
            }))
            
            if assertion_type == 'command':
                return self._execute_command_assertion(rds, token, host, step, step_result, assertion_config)
            elif assertion_type == 'output':
                return self._execute_output_assertion(rds, token, host, step, step_result, assertion_config, main_output)
            else:
                return False, f"不支持的断言类型: {assertion_type}"
                
        except Exception as e:
            error_msg = f"断言执行出错: {str(e)}"
            step_result.assertion_status = 'error'
            step_result.assertion_message = error_msg
            step_result.save()
            
            self.log_content.append(f'[{human_datetime()}]     ❌ {error_msg}')
            rds.publish(token, json.dumps({
                'type': 'error',
                'message': f'    ❌ {error_msg}'
            }))
            return False, error_msg
    
    def _execute_command_assertion(self, rds, token, host, step, step_result, assertion_config):
        """执行命令断言"""
        command_assertion = assertion_config.get('commandAssertion', {})
        command = command_assertion.get('command', '')
        description = command_assertion.get('description', '命令断言')
        
        if not command:
            return False, "命令断言配置为空"
        
        work_path = step.get('workPath', '/tmp')
        
        detail_msg = f'    💻 断言命令: {command}'
        self.log_content.append(f'[{human_datetime()}] {detail_msg}')
        rds.publish(token, json.dumps({
            'type': 'info',
            'message': detail_msg
        }))
        
        try:
            with host.get_ssh() as ssh:
                # 在同样的工作目录下执行断言命令
                if work_path:
                    full_command = f'cd {work_path} && {command}'
                else:
                    full_command = command
                
                try:
                    # 增加断言命令的超时时间到300秒（5分钟）
                    exit_code, output = ssh.exec_command_raw_with_timeout(full_command, timeout=300)
                except AttributeError:
                    exit_code, output = ssh.exec_command_raw(full_command)
                
                # 根据退出码判断断言结果
                success = (exit_code == 0)
                
                # 更新断言结果
                step_result.assertion_status = 'passed' if success else 'failed'
                step_result.assertion_result = json.dumps({
                    'command': command,
                    'exit_code': exit_code,
                    'output': output.strip()
                })
                step_result.assertion_message = description
                step_result.save()
                
                if success:
                    success_msg = f'    ✅ 断言通过: {description}'
                    self.log_content.append(f'[{human_datetime()}] {success_msg}')
                    rds.publish(token, json.dumps({
                        'type': 'success',
                        'message': success_msg
                    }))
                else:
                    fail_msg = f'    ❌ 断言失败: {description} (退出码: {exit_code})'
                    self.log_content.append(f'[{human_datetime()}] {fail_msg}')
                    rds.publish(token, json.dumps({
                        'type': 'error',
                        'message': fail_msg
                    }))
                
                return success, description
                
        except Exception as e:
            error_msg = f"命令断言执行出错: {str(e)}"
            step_result.assertion_status = 'error'
            step_result.assertion_message = error_msg
            step_result.save()
            return False, error_msg
    
    def _execute_output_assertion(self, rds, token, host, step, step_result, assertion_config, main_output):
        """执行输出匹配断言"""
        output_assertion = assertion_config.get('outputAssertion', {})
        pattern = output_assertion.get('pattern', '')
        match_type = output_assertion.get('type', 'contains')
        description = output_assertion.get('description', '输出匹配断言')
        
        if not pattern:
            return False, "输出匹配断言配置为空"
        
        detail_msg = f'    🔍 断言规则: {match_type} "{pattern}"'
        self.log_content.append(f'[{human_datetime()}] {detail_msg}')
        rds.publish(token, json.dumps({
            'type': 'info',
            'message': detail_msg
        }))
        
        try:
            success = False
            
            if match_type == 'contains':
                success = pattern in main_output
            elif match_type == 'equals':
                success = pattern.strip() == main_output.strip()
            elif match_type == 'regex':
                import re
                success = bool(re.search(pattern, main_output))
            else:
                return False, f"不支持的匹配类型: {match_type}"
            
            # 更新断言结果
            step_result.assertion_status = 'passed' if success else 'failed'
            step_result.assertion_result = json.dumps({
                'pattern': pattern,
                'match_type': match_type,
                'matched': success,
                'checked_output': main_output[:500] + ('...' if len(main_output) > 500 else '')
            })
            step_result.assertion_message = description
            step_result.save()
            
            if success:
                success_msg = f'    ✅ 断言通过: {description}'
                self.log_content.append(f'[{human_datetime()}] {success_msg}')
                rds.publish(token, json.dumps({
                    'type': 'success',
                    'message': success_msg
                }))
            else:
                fail_msg = f'    ❌ 断言失败: {description}'
                self.log_content.append(f'[{human_datetime()}] {fail_msg}')
                rds.publish(token, json.dumps({
                    'type': 'error',
                    'message': fail_msg
                }))
            
            return success, description
            
        except Exception as e:
            error_msg = f"输出断言执行出错: {str(e)}"
            step_result.assertion_status = 'error'
            step_result.assertion_message = error_msg
            step_result.save()
            return False, error_msg
    
    def _execute_command(self, rds, token, host, cmd, step_id, plan=None):
        """在主机上执行命令"""
        try:
            cmd_label = cmd.get('label', '命令执行')
            command = cmd['command']
            work_path = cmd.get('workPath', '/tmp')  # 获取执行路径，默认为/tmp
            
            # 获取测试计划的变量
            variables = []
            if plan:
                try:
                    variables = json.loads(plan.variables) if hasattr(plan, 'variables') else []
                except:
                    variables = []
            
            # 预处理命令和工作路径，处理变量替换和用户输入需求
            command = self._preprocess_command(command, variables)
            work_path = self._preprocess_command(work_path, variables)  # 也对工作路径进行变量替换
            
            # 初始化步骤日志内容
            step_log_content = []
            step_log_content.append(f'[{human_datetime()}] 开始执行步骤: {cmd_label}')
            step_log_content.append(f'[{human_datetime()}] 目标主机: {host.name}')
            step_log_content.append(f'[{human_datetime()}] 执行路径: {work_path}')
            step_log_content.append(f'[{human_datetime()}] 命令内容: {command}')
            
            # 添加变量替换日志
            if variables:
                step_log_content.append(f'[{human_datetime()}] 变量替换: {len(variables)} 个变量')
                for var in variables:
                    if var.get('name') and var.get('value') is not None:
                        step_log_content.append(f'[{human_datetime()}]   ${{{var["name"]}}} -> {var["value"]}')
            
            print(f"[DEBUG] Step workPath: {work_path}")
            print(f"[DEBUG] Step files: {cmd.get('files', [])}")
            print(f"[DEBUG] Executing command: {command} in {work_path}")
            
            exec_msg = f'    🔧 执行命令: {cmd_label}'
            work_path_msg = f'    📂 执行路径: {work_path}'
            command_msg = f'    💻 命令内容: {command}'
            self.log_content.append(f'[{human_datetime()}] {exec_msg}')
            self.log_content.append(f'[{human_datetime()}] {work_path_msg}')
            self.log_content.append(f'[{human_datetime()}] {command_msg}')
            rds.publish(token, json.dumps({
                'type': 'info',
                'message': exec_msg
            }))
            rds.publish(token, json.dumps({
                'type': 'info',
                'message': work_path_msg
            }))
            rds.publish(token, json.dumps({
                'type': 'info',
                'message': command_msg
            }))
            
            with host.get_ssh() as ssh:
                print(f"[DEBUG] SSH connection established for host: {host.name}")
                
                # 如果有执行路径，先确保目录存在，然后cd到指定目录
                if work_path:
                    # 确保工作目录存在
                    ssh.exec_command_raw(f'mkdir -p {work_path}')
                    full_command = f'cd {work_path} && {command}'
                else:
                    full_command = command
                    
                print(f"[DEBUG] Full command: {full_command}")
                step_log_content.append(f'[{human_datetime()}] 完整命令: {full_command}')
                
                # 使用带超时的执行方法，防止因用户输入而卡住
                try:
                    # 使用统一的系统超时配置
                    from .timeout_config import TEST_PLAN_TIMEOUT
                    step_timeout = TEST_PLAN_TIMEOUT['COMMAND_EXECUTION']  # 使用统一的超时配置
                    timeout_msg = f'    ⏰ 命令超时设置: {step_timeout} 秒 (系统统一配置)'
                    step_log_content.append(f'[{human_datetime()}] {timeout_msg}')
                    self.log_content.append(f'[{human_datetime()}] {timeout_msg}')
                    rds.publish(token, json.dumps({
                        'type': 'info',
                        'message': timeout_msg
                    }))
                    
                    exit_code, output = ssh.exec_command_raw_with_timeout(full_command, timeout=step_timeout)
                except AttributeError:
                    # 如果没有超时方法，使用普通方法
                    exit_code, output = ssh.exec_command_raw(full_command)
                
                print(f"[DEBUG] Command result - Exit code: {exit_code}, Output: {output}")
                step_log_content.append(f'[{human_datetime()}] 执行结果 - 退出码: {exit_code}')
                
                # 特殊处理超时情况
                if exit_code == -2:
                    # 命令执行超时，可能在等待用户输入
                    step = TestPlanStepResult.objects.get(id=step_id)
                    step.status = 'timeout'
                    step.end_time = human_datetime()
                    step.exit_code = exit_code
                    step.output = output or ''
                    
                    timeout_msg = f'    ⏰ {cmd_label} 执行超时 - 可能在等待用户输入'
                    step_log_content.append(f'[{human_datetime()}] {timeout_msg}')
                    self.log_content.append(f'[{human_datetime()}] {timeout_msg}')
                    rds.publish(token, json.dumps({
                        'type': 'warning',
                        'message': timeout_msg
                    }))
                    
                    suggestion_msg = f'    💡 建议：检查命令是否包含交互式输入要求'
                    step_log_content.append(f'[{human_datetime()}] {suggestion_msg}')
                    self.log_content.append(f'[{human_datetime()}] {suggestion_msg}')
                    rds.publish(token, json.dumps({
                        'type': 'info',
                        'message': suggestion_msg
                    }))
                    
                    # 保存步骤日志
                    self._save_step_log(cmd_label, step_log_content, host.name)
                    
                    return False
                
                # 更新步骤结果
                step = TestPlanStepResult.objects.get(id=step_id)
                step.status = 'success' if exit_code == 0 else 'failed'
                step.end_time = human_datetime()
                step.exit_code = exit_code
                step.output = output or ''
                
                # 记录输出到步骤日志
                if output:
                    clean_output = output.strip()
                    if clean_output:
                        step_log_content.append(f'[{human_datetime()}] 命令输出:')
                        step_log_content.append(clean_output)
                        
                        # 发布命令输出
                        output_msg = f'    📤 输出:\n{clean_output}'
                        self.log_content.append(f'[{human_datetime()}] {output_msg}')
                        rds.publish(token, json.dumps({
                            'type': 'output',
                            'message': output_msg
                        }))
                
                # 先保存步骤结果，然后检查是否需要执行断言
                step.save()
                
                command_success = (exit_code == 0)
                if command_success:
                    success_msg = f'    ✅ {cmd_label} 执行成功'
                    step_log_content.append(f'[{human_datetime()}] {success_msg}')
                    self.log_content.append(f'[{human_datetime()}] {success_msg}')
                    rds.publish(token, json.dumps({
                        'type': 'success',
                        'message': success_msg
                    }))
                else:
                    error_msg = f'    ❌ {cmd_label} 执行失败 (退出码: {exit_code})'
                    step_log_content.append(f'[{human_datetime()}] {error_msg}')
                    self.log_content.append(f'[{human_datetime()}] {error_msg}')
                    rds.publish(token, json.dumps({
                        'type': 'error', 
                        'message': error_msg
                    }))
                
                # 如果命令执行成功，或者配置了即使失败也执行断言，则执行断言
                assertion_config = cmd.get('assertion', {})
                if assertion_config.get('enabled') and (command_success or assertion_config.get('runOnFailure', False)):
                    try:
                        assertion_success, assertion_msg = self._execute_assertion(rds, token, host, cmd, step, output or '')
                        # 断言失败时，整体结果也是失败
                        if not assertion_success:
                            command_success = False
                            step_log_content.append(f'[{human_datetime()}] 断言失败: {assertion_msg}')
                        else:
                            step_log_content.append(f'[{human_datetime()}] 断言成功: {assertion_msg}')
                    except Exception as e:
                        print(f"[DEBUG] Assertion execution error: {str(e)}")
                        step_log_content.append(f'[{human_datetime()}] 断言执行错误: {str(e)}')
                        command_success = False
                
                # 保存步骤日志
                step_log_content.append(f'[{human_datetime()}] 步骤执行完成，结果: {"成功" if command_success else "失败"}')
                self._save_step_log(cmd_label, step_log_content, host.name)
                
                return command_success
                    
        except Exception as e:
            # 更新步骤结果
            step = TestPlanStepResult.objects.get(id=step_id)
            step.status = 'failed'
            step.end_time = human_datetime()
            step.output = str(e)
            
            exception_msg = f'    ❌ {cmd_label} 执行异常: {str(e)}'
            self.log_content.append(f'[{human_datetime()}] {exception_msg}')
            rds.publish(token, json.dumps({
                'type': 'error',
                'message': exception_msg
            }))
            
            # 保存异常步骤日志
            if hasattr(self, 'step_log_content'):
                step_log_content.append(f'[{human_datetime()}] 执行异常: {str(e)}')
                self._save_step_log(cmd_label, step_log_content, host.name)
            
            return False
    
    def _preprocess_command(self, command, variables=None):
        """预处理命令，处理变量替换和用户输入情况"""
        import re
        
        # 1. 变量替换 - 替换${variable}格式的变量
        if variables:
            for var in variables:
                var_name = var.get('name', '')
                var_value = var.get('value', '')
                if var_name and var_value is not None:
                    # 替换${variable_name}格式的变量
                    pattern = r'\$\{' + re.escape(var_name) + r'\}'
                    command = re.sub(pattern, str(var_value), command)
        
        # 2. 处理read命令，自动提供默认值
        # 例如：read -r device_id -> device_id="0"
        read_pattern = r'read\s+(-[a-z]+\s+)*([a-zA-Z_][a-zA-Z0-9_]*)'
        matches = re.findall(read_pattern, command)
        
        for match in matches:
            var_name = match[1] if isinstance(match, tuple) else match
            # 为常见变量名提供默认值
            default_values = {
                'device_id': '0',
                'size_choice': '1',
                'choice': '1',
                'answer': 'y',
                'confirm': 'y',
                'device': '0',
                'option': '1'
            }
            default_value = default_values.get(var_name, '""')
            
            # 替换read命令为直接赋值
            old_read = f'read -r {var_name}'
            new_assignment = f'{var_name}={default_value}'
            command = command.replace(old_read, new_assignment)
            
            # 处理带提示的read命令
            prompt_pattern = f'read -r -p "[^"]*" {var_name}'
            command = re.sub(prompt_pattern, new_assignment, command)
        
        # 处理交互式工具，自动添加非交互参数
        interactive_tools = {
            'apt-get': '--yes --force-yes',
            'yum': '-y',
            'dnf': '-y',
            'pip': '--yes',
            'npm': '--yes',
            'docker': '--interactive=false'
        }
        
        for tool, params in interactive_tools.items():
            if tool in command and params not in command:
                command = command.replace(tool, f'{tool} {params}')
        
        # 处理确认提示，自动回答yes
        if 'read -r -p' in command and '请选择' in command:
            # 为选择菜单自动选择第一个选项
            command = f'echo "自动选择选项1" && {command.replace("read -r -p", "# read -r -p")}'
        
        return command
    
    def _save_execution_log(self, execution, log_content):
        """保存执行日志到文件管理系统"""
        try:
            import os
            from django.conf import settings
            
            # 创建日志目录
            log_dir = os.path.join(settings.BASE_DIR, 'files', '执行日志')
            os.makedirs(log_dir, exist_ok=True)
            
            # 生成日志文件名
            log_filename = f"测试计划_{execution.plan_name}_{execution.start_time.replace(':', '-').replace(' ', '_')}.log"
            log_path = os.path.join(log_dir, log_filename)
            
            # 写入日志文件
            with open(log_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(log_content))
            
            # 更新执行记录，添加日志文件路径和步骤日志文件夹路径
            execution.log_file = f'执行日志/{log_filename}'
            
            # 如果创建了步骤日志文件夹，也保存到数据库
            if hasattr(self, 'step_logs_folder_name') and self.step_logs_folder_name:
                execution.step_logs_folder = self.step_logs_folder_name
            
            execution.save()
            
            print(f"[DEBUG] 日志文件已保存: {log_path}")
            if hasattr(self, 'step_logs_folder_name'):
                print(f"[DEBUG] 步骤日志文件夹: {self.step_logs_folder_name}")
            
        except Exception as e:
            print(f"[DEBUG] 保存日志文件失败: {str(e)}")
            import traceback
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")

    def _init_step_logs_folder(self, execution):
        """初始化步骤日志文件夹"""
        try:
            import os
            from django.conf import settings
            
            # 创建主日志目录
            log_dir = os.path.join(settings.BASE_DIR, 'files', '执行日志')
            os.makedirs(log_dir, exist_ok=True)
            
            # 生成文件夹名称（与日志文件同名）
            folder_name = f"测试计划_{execution.plan_name}_{execution.start_time.replace(':', '-').replace(' ', '_')}"
            self.step_logs_folder = os.path.join(log_dir, folder_name)
            
            # 创建步骤日志文件夹
            os.makedirs(self.step_logs_folder, exist_ok=True)
            
            # 保存文件夹路径到实例变量，供其他方法使用
            self.step_logs_folder_name = f'执行日志/{folder_name}'
            
            print(f"[DEBUG] 步骤日志文件夹已创建: {self.step_logs_folder}")
            
        except Exception as e:
            print(f"[DEBUG] 创建步骤日志文件夹失败: {str(e)}")
            self.step_logs_folder = None
            self.step_logs_folder_name = None

    def _save_step_log(self, step_name, step_content, host_name=None):
        """保存单个步骤的日志"""
        try:
            if not hasattr(self, 'step_logs_folder') or not self.step_logs_folder:
                return
            
            # 生成步骤日志文件名
            safe_step_name = step_name.replace('/', '_').replace('\\', '_').replace(':', '_')
            if host_name:
                step_log_filename = f"{safe_step_name}_{host_name}.log"
            else:
                step_log_filename = f"{safe_step_name}.log"
            
            step_log_path = os.path.join(self.step_logs_folder, step_log_filename)
            
            # 写入步骤日志文件
            with open(step_log_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(step_content))
            
            print(f"[DEBUG] 步骤日志已保存: {step_log_path}")
            
        except Exception as e:
            print(f"[DEBUG] 保存步骤日志失败: {str(e)}")
    
    def _check_ssh_connection(self, rds, token, host):
        """检测SSH连接可行性"""
        try:
            check_msg = f'🔍 检测SSH连接: {host.name} ({host.hostname})'
            self.log_content.append(f'[{human_datetime()}] {check_msg}')
            rds.publish(token, json.dumps({
                'type': 'info',
                'message': check_msg
            }))
            
            # 尝试建立SSH连接并执行简单命令
            with host.get_ssh() as ssh:
                print(f"[DEBUG] SSH connection test for host: {host.name}")
                
                # 执行简单的测试命令
                exit_code, output = ssh.exec_command_raw('echo "SSH connection test"')
                
                if exit_code == 0:
                    success_msg = f'✅ SSH连接正常: {host.name}'
                    self.log_content.append(f'[{human_datetime()}] {success_msg}')
                    rds.publish(token, json.dumps({
                        'type': 'success',
                        'message': success_msg
                    }))
                    return True
                else:
                    error_msg = f'❌ SSH连接测试失败: {host.name} (退出码: {exit_code})'
                    self.log_content.append(f'[{human_datetime()}] {error_msg}')
                    rds.publish(token, json.dumps({
                        'type': 'error',
                        'message': error_msg
                    }))
                    return False
                    
        except Exception as e:
            error_msg = f'❌ SSH连接异常: {host.name} - {str(e)}'
            self.log_content.append(f'[{human_datetime()}] {error_msg}')
            rds.publish(token, json.dumps({
                'type': 'error',
                'message': error_msg
            }))
            
            # 提供详细的错误诊断
            if 'timeout' in str(e).lower():
                timeout_msg = f'💡 连接超时建议: 检查网络连通性和防火墙设置'
                self.log_content.append(f'[{human_datetime()}] {timeout_msg}')
                rds.publish(token, json.dumps({
                    'type': 'warning',
                    'message': timeout_msg
                }))
            elif 'authentication' in str(e).lower() or 'auth' in str(e).lower():
                auth_msg = f'💡 认证失败建议: 检查SSH密钥或密码配置'
                self.log_content.append(f'[{human_datetime()}] {auth_msg}')
                rds.publish(token, json.dumps({
                    'type': 'warning',
                    'message': auth_msg
                }))
            elif 'connection refused' in str(e).lower():
                refused_msg = f'💡 连接被拒绝建议: 检查SSH服务是否启动和端口配置'
                self.log_content.append(f'[{human_datetime()}] {refused_msg}')
                rds.publish(token, json.dumps({
                    'type': 'warning',
                    'message': refused_msg
                }))
            
            return False


# 安全的初始化示例数据 - 只在完全没有数据时才添加
def init_test_plans():
    # 检查是否已有用户创建的测试计划，如果有则不初始化示例数据
    pass
    # if not TestPlan.objects.exists():
    #     # 使用新的步骤化数据结构
    #     sample_plan = TestPlan(
    #         id=1,
    #         name='GPU驱动升级测试计划',
    #         description='包含驱动文件分发和升级命令的完整测试计划',
    #         category='upgrade',
    #         commands=[
    #             {
    #                 'id': 'step1',
    #                 'label': '环境检查',
    #                 'command': 'echo "开始环境检查..." && df -h /tmp && echo "磁盘空间检查完成" && lsmod | grep nvidia || echo "未发现nvidia模块"',
    #                 'description': '检查当前驱动状态和磁盘空间',
    #                 'enabled': True,
    #                 'workPath': '/tmp',  # 添加执行路径
    #                 'files': []  # 这个步骤不需要文件
    #             },
    #             {
    #                 'id': 'step2',
    #                 'label': '文件准备',
    #                 'command': 'chmod +x /tmp/nvidia-driver-460.run',
    #                 'description': '设置驱动文件权限',
    #                 'enabled': True,
    #                 'workPath': '/tmp',  # 添加执行路径
    #                 'files': [
    #                     {
    #                         'name': 'nvidia-driver-460.run',
    #                         'path': '固件/nvidia-driver-460.run',
    #                         'targetPath': '/tmp/nvidia-driver-460.run'
    #                     }
    #                 ]
    #             },
    #             {
    #                 'id': 'step3',
    #                 'label': '备份当前驱动',
    #                 'command': 'cp /usr/bin/nvidia-smi /tmp/nvidia-smi.backup 2>/dev/null || echo "没有找到现有驱动"',
    #                 'description': '备份当前驱动程序',
    #                 'enabled': True,
    #                 'workPath': '/usr/bin',  # 添加执行路径
    #                 'files': []
    #             },
    #             {
    #                 'id': 'step4',
    #                 'label': '安装驱动',
    #                 'command': '/tmp/nvidia-driver-460.run --silent --no-kernel-module-source',
    #                 'description': '执行驱动安装',
    #                 'enabled': True,
    #                 'workPath': '/tmp',  # 添加执行路径
    #                 'files': []
    #             },
    #             {
    #                 'id': 'step5',
    #                 'label': '验证安装',
    #                 'command': 'nvidia-smi && echo "驱动安装验证成功" || echo "nvidia-smi命令不可用，可能需要重启系统"',
    #                 'description': '验证驱动安装结果',
    #                 'enabled': True,
    #                 'workPath': '/usr/bin',  # 添加执行路径
    #                 'files': []
    #             }
    #         ],
    #         created_at=human_datetime(),
    #         updated_at=human_datetime()
    #     )
    #     sample_plan.save()
    #     
    #     # 添加第二个示例计划
    #     performance_plan = TestPlan(
    #         id=2,
    #         name='GPU性能测试计划',
    #         description='包含GPU性能测试脚本和命令的测试计划',
    #         category='performance',
    #         commands=[
    #             {
    #                 'id': 'step1',
    #                 'label': '系统信息',
    #                 'command': 'uname -a && lscpu | head -20',
    #                 'description': '获取系统基本信息',
    #                 'enabled': True,
    #                 'workPath': '/tmp',  # 添加执行路径
    #                 'files': []
    #             },
    #             {
    #                 'id': 'step2',
    #                 'label': 'GPU信息',
    #                 'command': 'nvidia-smi -q -d MEMORY,UTILIZATION,TEMPERATURE',
    #                 'description': '获取GPU详细信息',
    #                 'enabled': True,
    #                 'workPath': '/usr/bin',  # 添加执行路径
    #                 'files': []
    #             },
    #             {
    #                 'id': 'step3',
    #                 'label': '运行性能测试',
    #                 'command': 'python3 /tmp/gpu_benchmark.py',
    #                 'description': '执行GPU性能基准测试',
    #                 'enabled': True,
    #                 'workPath': '/tmp',  # 添加执行路径
    #                 'files': [
    #                     {
    #                         'name': 'gpu_benchmark.py',
    #                         'path': '脚本/gpu_benchmark.py',
    #                         'targetPath': '/tmp/gpu_benchmark.py'
    #                     }
    #                 ]
    #             }
    #         ],
    #         created_at=human_datetime(),
    #         updated_at=human_datetime()
    #     )
    #     performance_plan.save()

# 只在首次启动时初始化示例数据（如果没有任何测试计划）
# 注释掉自动初始化，避免覆盖用户数据
# init_test_plans()

# 初始化一些测试执行记录
def init_test_executions():
    pass
    # if not TestPlanExecution.objects.exists():
    #     # 添加一些测试执行记录
    #     test_execution_1 = TestPlanExecution(
    #         id=1,
    #         token='test_token_1',
    #         plan_id=1,
    #         plan_name='GPU驱动升级测试计划',
    #         status='completed',
    #         start_time='2025-06-08 11:59:45',
    #         end_time='2025-06-08 11:59:49',
    #         user_name='管理员',
    #         host_ids=[1],
    #         log_file='执行日志/测试计划_GPU驱动升级测试计划_2025-06-08_11-59-45.log'
    #     )
    #     test_execution_1.save()
    #     
    #     test_execution_2 = TestPlanExecution(
    #         id=2,
    #         token='test_token_2',
    #         plan_id=2,
    #         plan_name='GPU性能测试计划',
    #         status='success',
    #         start_time='2025-06-08 11:25:13',
    #         end_time='2025-06-08 11:25:16',
    #         user_name='管理员',
    #         host_ids=[1],
    #         log_file='执行日志/测试计划_GPU性能测试计划_2025-06-08_11-25-13.log'
    #     )
    #     test_execution_2.save()

# 调用执行记录初始化函数
init_test_executions()


class TestPlanExecutionHistoryView(View):
    """测试计划执行历史记录管理"""
    
    @auth('exec.task.do')
    def get(self, request):
        """获取执行历史记录列表"""
        # 返回所有执行记录，按时间倒序
        executions = sorted(TestPlanExecution.objects.all(), key=lambda x: x.start_time, reverse=True)
        
        # 简化数据，只返回列表需要的字段
        result = []
        for exec_record in executions:
            result.append({
                'id': exec_record.id,
                'token': exec_record.token,
                'plan_name': exec_record.plan_name,
                'status': exec_record.status,
                'start_time': exec_record.start_time,
                'end_time': exec_record.end_time,
                'user_name': exec_record.executor_name,
                'host_count': len(json.loads(exec_record.host_ids)),
                'log_file': exec_record.log_file,
                'step_logs_folder': exec_record.step_logs_folder
            })
        
        return json_response({'data': result})
    
    @auth('exec.task.do')
    def delete(self, request, execution_id):
        """删除执行记录"""
        try:
            execution_id = int(execution_id)
            # 找到并删除执行记录
            execution = next((exec for exec in TestPlanExecution.objects.all() if exec.id == execution_id), None)
            if not execution:
                return json_response(error='执行记录不存在')
            
            # 删除日志文件
            if execution.log_file:
                try:
                    import os
                    from django.conf import settings
                    log_path = os.path.join(settings.BASE_DIR, 'files', execution.log_file)
                    if os.path.exists(log_path):
                        os.remove(log_path)
                except Exception as e:
                    print(f"[DEBUG] 删除日志文件失败: {str(e)}")
            
            # 删除步骤日志文件夹
            if execution.step_logs_folder:
                try:
                    import os
                    import shutil
                    from django.conf import settings
                    step_logs_path = os.path.join(settings.BASE_DIR, 'files', execution.step_logs_folder)
                    if os.path.exists(step_logs_path):
                        shutil.rmtree(step_logs_path)
                        print(f"[DEBUG] 步骤日志文件夹已删除: {step_logs_path}")
                except Exception as e:
                    print(f"[DEBUG] 删除步骤日志文件夹失败: {str(e)}")
            
            # 删除执行记录
            execution.delete()
            
            return json_response({'message': '删除成功'})
            
        except ValueError:
            return json_response(error='无效的执行记录ID')
        except Exception as e:
            return json_response(error=f'删除失败: {str(e)}')


class SampleDataView(View):
    """示例数据管理API - 仅用于开发和测试"""
    
    @auth('exec.task.do')
    def post(self, request):
        """手动初始化示例测试计划数据"""
        try:
            # 检查是否已有数据
            if TestPlan.objects.exists():
                return json_response(error='已存在测试计划数据，无需初始化示例数据')
            
            # 调用初始化函数
            init_test_plans()
            
            return json_response({
                'message': '示例数据初始化成功',
                'count': TestPlan.objects.count()
            })
            
        except Exception as e:
            return json_response(error=f'初始化失败: {str(e)}')
    
    @auth('exec.task.do')  
    def delete(self, request):
        """清空所有测试计划数据（谨慎操作）"""
        try:
            backup_data = TestPlan.objects.all().values()
            TestPlan.objects.all().delete()
            
            return json_response({
                'message': '所有测试计划数据已清空',
                'backup_count': len(backup_data)
            })
            
        except Exception as e:
            return json_response(error=f'清空失败: {str(e)}')


class TestCaseSetView(View):
    """测试用例集管理API"""

    @auth('exec.task.do')  # 使用已有的执行权限
    def get(self, request, case_set_id=None):
        """获取测试用例集列表或单个测试用例集"""
        if case_set_id:
            # 获取单个测试用例集
            try:
                case_set = TestCaseSet.objects.get(id=int(case_set_id))
                return json_response({'data': case_set.to_dict()})
            except TestCaseSet.DoesNotExist:
                return json_response(error='测试用例集不存在')
        else:
            # 获取所有测试用例集
            case_sets = TestCaseSet.objects.all()
            return json_response({'data': [case_set.to_dict() for case_set in case_sets]})

    @auth('exec.task.do')
    def post(self, request):
        """创建新的测试用例集"""
        try:
            data = json.loads(request.body)

            new_case_set = TestCaseSet(
                name=data.get('name', ''),
                description=data.get('description', ''),
                category=data.get('category', ''),
                test_cases=json.dumps(data.get('test_cases', [])),
                created_by_id=1,  # 暂时使用默认用户ID
                created_at=human_datetime(),
                updated_at=human_datetime()
            )

            new_case_set.save()

            return json_response({
                'message': '测试用例集创建成功',
                'case_set': new_case_set.to_dict()
            })

        except Exception as e:
            return json_response(error=f'创建失败: {str(e)}')

    @auth('exec.task.do')
    def put(self, request, case_set_id):
        """更新测试用例集"""
        try:
            data = json.loads(request.body)
            case_set_id = int(case_set_id)

            # 查找要更新的测试用例集
            try:
                case_set = TestCaseSet.objects.get(id=case_set_id)
            except TestCaseSet.DoesNotExist:
                return json_response(error='测试用例集不存在')

            # 更新字段
            case_set.name = data.get('name', case_set.name)
            case_set.description = data.get('description', case_set.description)
            case_set.category = data.get('category', case_set.category)
            case_set.test_cases = json.dumps(data.get('test_cases', []))



            case_set.updated_at = human_datetime()
            case_set.updated_by_id = 1  # 暂时使用默认用户ID

            case_set.save()

            return json_response({
                'message': '测试用例集更新成功',
                'case_set': case_set.to_dict()
            })

        except Exception as e:
            return json_response(error=f'更新失败: {str(e)}')

    @auth('exec.task.do')
    def delete(self, request, case_set_id):
        """删除测试用例集"""
        try:
            case_set_id = int(case_set_id)

            # 查找要删除的测试用例集
            try:
                case_set = TestCaseSet.objects.get(id=case_set_id)
                case_set.delete()
            except TestCaseSet.DoesNotExist:
                return json_response(error='测试用例集不存在')

            return json_response({
                'message': '测试用例集删除成功'
            })

        except Exception as e:
            return json_response(error=f'删除失败: {str(e)}')





class SSHConnectionTestView(View):
    """SSH连接测试视图"""
    
    @auth('exec.task.do')
    def post(self, request):
        """测试SSH连接"""
        try:
            from apps.host.models import Host
            
            data = json.loads(request.body)
            host_ids = data.get('host_ids', [])
            
            if not host_ids:
                return json_response(error='请选择要测试的主机')
            
            results = []
            
            for host_id in host_ids:
                try:
                    host = Host.objects.get(pk=host_id)
                    
                    # 测试SSH连接
                    try:
                        with host.get_ssh() as ssh:
                            exit_code, output = ssh.exec_command_raw('echo "SSH connection test"')
                            
                            if exit_code == 0:
                                results.append({
                                    'host_id': host_id,
                                    'host_name': host.name,
                                    'hostname': host.hostname,
                                    'status': 'success',
                                    'message': 'SSH连接正常',
                                    'details': output.strip() if output else ''
                                })
                            else:
                                results.append({
                                    'host_id': host_id,
                                    'host_name': host.name,
                                    'hostname': host.hostname,
                                    'status': 'failed',
                                    'message': f'SSH连接测试失败 (退出码: {exit_code})',
                                    'details': output.strip() if output else ''
                                })
                                
                    except Exception as ssh_e:
                        error_type = 'unknown'
                        suggestion = '请检查主机配置和网络连接'
                        
                        if 'timeout' in str(ssh_e).lower():
                            error_type = 'timeout'
                            suggestion = '连接超时，请检查网络连通性和防火墙设置'
                        elif 'authentication' in str(ssh_e).lower() or 'auth' in str(ssh_e).lower():
                            error_type = 'authentication'
                            suggestion = '认证失败，请检查SSH密钥或密码配置'
                        elif 'connection refused' in str(ssh_e).lower():
                            error_type = 'connection_refused'
                            suggestion = '连接被拒绝，请检查SSH服务是否启动和端口配置'
                        
                        results.append({
                            'host_id': host_id,
                            'host_name': host.name,
                            'hostname': host.hostname,
                            'status': 'error',
                            'message': f'SSH连接异常: {str(ssh_e)}',
                            'error_type': error_type,
                            'suggestion': suggestion,
                            'details': str(ssh_e)
                        })
                        
                except Host.DoesNotExist:
                    results.append({
                        'host_id': host_id,
                        'host_name': '未知',
                        'hostname': '未知',
                        'status': 'error',
                        'message': '主机不存在',
                        'details': f'主机ID {host_id} 不存在'
                    })
            
            # 统计结果
            success_count = len([r for r in results if r['status'] == 'success'])
            failed_count = len([r for r in results if r['status'] in ['failed', 'error']])
            
            return json_response({
                'results': results,
                'summary': {
                    'total': len(results),
                    'success': success_count,
                    'failed': failed_count
                }
            })
            
        except Exception as e:
            return json_response(error=f'SSH连接测试失败: {str(e)}')


class TestResultView(View):
    """测试结果收集API"""
    
    @auth('exec.task.do')
    def get(self, request, result_id=None):
        """获取测试结果列表或单个测试结果"""
        try:
            from .models import TestResult
            
            if result_id:
                # 获取单个测试结果
                try:
                    result = TestResult.objects.get(pk=result_id)
                    
                    # 获取并处理指标数据
                    raw_metrics = result.get_metrics()
                    processed_metrics = []
                    
                    if raw_metrics and isinstance(raw_metrics, list):
                        for i, metric in enumerate(raw_metrics):
                            if isinstance(metric, dict):
                                processed_metric = {
                                    'name': metric.get('label') or metric.get('name') or metric.get('metric_name') or f'指标{i+1}',
                                    'value': metric.get('value', '未知'),
                                    'unit': metric.get('unit', ''),
                                    'type': metric.get('type', 'performance'),
                                    'confidence': metric.get('confidence', 0.0),
                                    'confirmed': metric.get('confirmed', False),
                                    'source': metric.get('source', None)
                                }
                                processed_metrics.append(processed_metric)
                    
                    result_data = {
                        'id': result.id,
                        'execution_id': result.execution.id,
                        'plan_name': result.plan_name,
                        'task_name': result.task_name,
                        'metrics': processed_metrics,  # 处理后的指标数据
                        'raw_log': result.raw_log,
                        'total_metrics': result.total_metrics,
                        'confirmed_metrics': result.confirmed_metrics,
                        'ai_confidence': result.ai_confidence,
                        'success_rate': result.success_rate,
                        'created_at': result.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                        'created_by': f'用户ID_{result.created_by_id}'
                    }
                    

                    return json_response(result_data)
                except TestResult.DoesNotExist:
                    return json_response(error='测试结果不存在')
            else:
                # 获取测试结果列表
                execution_id = request.GET.get('execution_id')
                plan_name = request.GET.get('plan_name')
                task_name = request.GET.get('task_name')
                
                queryset = TestResult.objects.select_related('execution').all()
                
                if execution_id:
                    queryset = queryset.filter(execution_id=execution_id)
                if plan_name:
                    queryset = queryset.filter(plan_name__icontains=plan_name)
                if task_name:
                    queryset = queryset.filter(task_name__icontains=task_name)
                
                results = []
                for result in queryset:
                    # 获取指标数据和指标名称
                    metrics = result.get_metrics()
                    metric_names = []
                    
                    # 从指标数据中提取名称
                    if metrics and isinstance(metrics, list):
                        for metric in metrics:
                            if isinstance(metric, dict):
                                name = metric.get('label') or metric.get('name') or metric.get('metric_name')
                                if name:
                                    metric_names.append(name)
                    
                    result_data = {
                        'id': result.id,
                        'execution_id': result.execution.id,
                        'plan_name': result.plan_name,
                        'task_name': result.task_name,
                        'metrics': metrics,  # 完整的指标数据
                        'metric_names': metric_names,  # 指标名称列表
                        'total_metrics': result.total_metrics,
                        'confirmed_metrics': result.confirmed_metrics,
                        'success_rate': result.success_rate,
                        'ai_confidence': result.ai_confidence,
                        'created_at': result.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                        'created_by': f'用户ID_{result.created_by_id}'
                    }
                    

                    
                    results.append(result_data)
                
                return json_response({'data': results})
                
        except Exception as e:
            return json_response(error=f'获取测试结果失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """保存测试结果"""
        try:
            from .models import TestResult
            from django.db import transaction
            from django.utils import timezone
            
            data = json.loads(request.body)
            execution_id = data.get('execution_id')
            plan_name = data.get('plan_name')
            task_name = data.get('task_name', '')
            metrics = data.get('metrics', [])
            raw_log = data.get('raw_log', '')
            confirmed_metrics = data.get('confirmed_metrics', 0)
            ai_confidence = data.get('ai_confidence', 0.0)
            

            
            if not execution_id:
                return json_response(error='缺少执行记录ID')
            
            if not plan_name:
                return json_response(error='缺少测试计划名称')
            
            if not metrics:
                return json_response(error='请至少提供一个性能指标')
            
            # 验证执行记录是否存在
            try:
                from .models import TestPlanExecution
                execution = TestPlanExecution.objects.get(pk=execution_id)

            except TestPlanExecution.DoesNotExist:
                # 列出所有可用的TestPlanExecution记录
                all_executions = TestPlanExecution.objects.all()
                available_ids = [str(h.id) for h in all_executions]
                return json_response(error=f'执行记录ID {execution_id} 不存在。可用的ID: {", ".join(available_ids)}')
            
            # 使用Django ORM和事务来避免数据库锁定问题
            with transaction.atomic():
                try:
                    # 获取用户ID
                    user_id = getattr(execution, 'executor_id', 1)
                    
                    # 准备metrics数据
                    metrics_json = json.dumps(metrics, ensure_ascii=False)
                    
                    # 检查是否已有该执行记录的测试结果
                    existing_result = TestResult.objects.filter(execution_id=execution_id).first()
                    
                    if existing_result:
                        # 更新现有结果
                        existing_result.plan_name = plan_name
                        existing_result.task_name = task_name
                        existing_result.metrics = metrics_json
                        existing_result.raw_log = raw_log
                        existing_result.total_metrics = len(metrics)
                        existing_result.confirmed_metrics = confirmed_metrics
                        existing_result.ai_confidence = ai_confidence
                        existing_result.updated_at = timezone.now()
                        existing_result.save()
                        
                        return json_response({
                            'message': '测试结果已更新',
                            'result_id': existing_result.id
                        })
                    else:
                        # 创建新的测试结果
                        new_result = TestResult.objects.create(
                            execution_id=execution_id,
                            plan_name=plan_name,
                            task_name=task_name,
                            metrics=metrics_json,
                            raw_log=raw_log,
                            total_metrics=len(metrics),
                            confirmed_metrics=confirmed_metrics,
                            ai_confidence=ai_confidence,
                            created_by_id=user_id
                        )
                        
                        return json_response({
                            'message': '测试结果已保存',
                            'result_id': new_result.id
                        })
                        
                except Exception as e:
                    print(f"[ERROR] 保存测试结果失败: {str(e)}")
                    return json_response(error=f'保存测试结果失败: {str(e)}')
                
        except Exception as e:

            return json_response(error=f'保存测试结果失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, result_id):
        """更新测试结果"""
        try:
            from .models import TestResult
            
            try:
                result = TestResult.objects.get(pk=result_id)
            except TestResult.DoesNotExist:
                return json_response(error='测试结果不存在')
            
            data = json.loads(request.body)
            
            # 更新字段
            if 'plan_name' in data:
                result.plan_name = data['plan_name']
            if 'task_name' in data:
                result.task_name = data['task_name']
            if 'confirmed_metrics' in data:
                confirmed_metrics = int(data['confirmed_metrics'])
                if confirmed_metrics < 0:
                    return json_response(error='已确认指标数量不能为负数')
                if confirmed_metrics > result.total_metrics:
                    return json_response(error='已确认指标数量不能超过总指标数量')
                result.confirmed_metrics = confirmed_metrics
            
            result.save()
            
            return json_response({'message': '更新成功'})
            
        except Exception as e:
            return json_response(error=f'更新失败: {str(e)}')
    
    @auth('exec.task.do')
    def delete(self, request, result_id):
        """删除测试结果"""
        try:
            from .models import TestResult
            
            try:
                result = TestResult.objects.get(pk=result_id)
                result.delete()
                return json_response({'message': '删除成功'})
            except TestResult.DoesNotExist:
                return json_response(error='测试结果不存在')
                
        except Exception as e:
            return json_response(error=f'删除失败: {str(e)}')


class TestResultTemplateView(View):
    """测试结果提取模板API"""
    
    @auth('exec.task.do')
    def get(self, request, template_id=None):
        """获取模板列表或单个模板"""
        try:
            from .models import TestResultTemplate
            
            if template_id:
                # 获取单个模板
                try:
                    template = TestResultTemplate.objects.get(pk=template_id)
                    template_data = {
                        'id': template.id,
                        'name': template.name,
                        'description': template.description,
                        'category': template.category,
                        'expected_metrics': template.get_expected_metrics(),
                        'extraction_patterns': template.get_extraction_patterns(),
                        'validation_rules': template.get_validation_rules(),
                        'is_active': template.is_active,
                        'created_at': template.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                        'created_by': f'用户ID_{template.created_by_id}'
                    }
                    return json_response(template_data)
                except TestResultTemplate.DoesNotExist:
                    return json_response(error='模板不存在')
            else:
                # 获取模板列表
                category = request.GET.get('category')
                is_active = request.GET.get('is_active', 'true')
                
                queryset = TestResultTemplate.objects.all()
                
                if category:
                    queryset = queryset.filter(category=category)
                if is_active.lower() == 'true':
                    queryset = queryset.filter(is_active=True)
                
                templates = []
                for template in queryset:
                    templates.append({
                        'id': template.id,
                        'name': template.name,
                        'description': template.description,
                        'category': template.category,
                        'is_active': template.is_active,
                        'created_at': template.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                        'created_by': f'用户ID_{template.created_by_id}'
                    })
                return json_response({'data': templates})
                
        except Exception as e:
            return json_response(error=f'获取模板失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """创建新模板"""
        try:
            from .models import TestResultTemplate
            
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description', '')
            category = data.get('category')
            extraction_rules = data.get('extraction_rules', [])
            
            if not name:
                return json_response(error='请输入模板名称')
            
            if not category:
                return json_response(error='请选择模板分类')
            
            # 检查名称是否已存在
            if TestResultTemplate.objects.filter(name=name).exists():
                return json_response(error='模板名称已存在')
            
            user_id = getattr(request.user, 'id', 1)
            template = TestResultTemplate.objects.create(
                name=name,
                description=description,
                category=category,
                expected_metrics=json.dumps(data.get('expected_metrics', []), ensure_ascii=False),
                extraction_patterns=json.dumps(extraction_rules, ensure_ascii=False),
                validation_rules=json.dumps(data.get('validation_rules', {}), ensure_ascii=False),
                created_by_id=user_id
            )
            
            return json_response({
                'message': '模板创建成功',
                'template_id': template.id
            })
            
        except Exception as e:
            return json_response(error=f'创建模板失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, template_id):
        """更新模板"""
        try:
            from .models import TestResultTemplate
            
            try:
                template = TestResultTemplate.objects.get(pk=template_id)
            except TestResultTemplate.DoesNotExist:
                return json_response(error='模板不存在')
            
            data = json.loads(request.body)
            
            # 更新字段
            if 'name' in data:
                template.name = data['name']
            if 'description' in data:
                template.description = data['description']
            if 'category' in data:
                template.category = data['category']
            if 'expected_metrics' in data:
                template.expected_metrics = json.dumps(data['expected_metrics'], ensure_ascii=False)
            if 'extraction_patterns' in data:
                template.extraction_patterns = json.dumps(data['extraction_patterns'], ensure_ascii=False)
            if 'validation_rules' in data:
                template.validation_rules = json.dumps(data['validation_rules'], ensure_ascii=False)
            if 'is_active' in data:
                template.is_active = data['is_active']
            
            template.save()
            
            return json_response({'message': '模板更新成功'})
            
        except Exception as e:
            return json_response(error=f'更新模板失败: {str(e)}')
    
    @auth('exec.task.do')
    def delete(self, request, template_id):
        """删除模板"""
        try:
            from .models import TestResultTemplate
            
            try:
                template = TestResultTemplate.objects.get(pk=template_id)
                template.delete()
                return json_response({'message': '删除成功'})
            except TestResultTemplate.DoesNotExist:
                return json_response(error='模板不存在')
                
        except Exception as e:
            return json_response(error=f'删除失败: {str(e)}')


@method_decorator(csrf_exempt, name='dispatch')
class TestCaseSetImportExportView(View):
    """测试用例集导入导出API"""

    @auth('exec.task.do')
    def get(self, request):
        """导出测试用例集或下载模板"""
        action = request.GET.get('action', 'export')

        if action == 'template':
            return self._download_template()
        else:
            return self._export_case_sets(request)

    def _download_template(self):
        """下载导入模板"""
        try:
            import os
            import urllib.parse
            from django.http import HttpResponse
            from django.conf import settings
            import openpyxl
            from openpyxl.styles import Font, Alignment, PatternFill
            from io import BytesIO

            # 创建新的工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "测试用例集导入模板"

            # 定义表头
            headers = [
                '用例名称',      # 必填
                '预置条件',      # 可选
                '测试步骤',      # 必填
                '预期结果'       # 必填
            ]

            # 设置表头样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")

            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment

            # 添加示例数据
            example_data = [
                ['基础功能测试', '1. 启动应用\n2. 执行基础操作', '1. 打开应用\n2. 验证界面显示\n3. 执行基本功能', '符合预期'],
                ['性能测试', '系统正常运行', '1. 执行性能测试\n2. 记录响应时间', '响应时间在可接受范围内']
            ]

            for row_idx, row_data in enumerate(example_data, 2):
                for col_idx, value in enumerate(row_data, 1):
                    ws.cell(row=row_idx, column=col_idx, value=value)

            # 设置列宽
            column_widths = [20, 30, 40, 30]
            for i, width in enumerate(column_widths, 1):
                ws.column_dimensions[openpyxl.utils.get_column_letter(i)].width = width

            # 保存到内存
            output = BytesIO()
            wb.save(output)
            output.seek(0)
            file_content = output.getvalue()
            output.close()

            # 创建HttpResponse
            response = HttpResponse(file_content)
            response['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

            # 设置文件名
            filename = '测试用例集导入模板.xlsx'
            encoded_filename = urllib.parse.quote(filename)
            response['Content-Disposition'] = f'attachment; filename="{filename}"; filename*=UTF-8\'\'{encoded_filename}'
            response['Content-Length'] = len(file_content)

            # 添加CORS头
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept, X-Token'

            return response

        except Exception as e:
            return json_response(error=f"下载模板失败: {e}", status_code=500)

    def _export_case_sets(self, request):
        """导出测试用例集"""
        try:
            # 获取要导出的用例集ID列表
            case_set_ids = request.GET.get('case_set_ids', '')
            export_all = request.GET.get('export_all', 'false').lower() == 'true'

            if export_all:
                case_sets = TestCaseSet.objects.all()
            elif case_set_ids:
                id_list = [int(x.strip()) for x in case_set_ids.split(',') if x.strip().isdigit()]
                case_sets = TestCaseSet.objects.filter(id__in=id_list)
            else:
                return json_response(error='请指定要导出的测试用例集')

            import openpyxl
            from openpyxl.styles import Font, Alignment, PatternFill
            from io import BytesIO
            from django.http import HttpResponse
            import urllib.parse

            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "测试用例集导出"

            # 设置表头
            headers = ['用例集名称', '用例名称', '预置条件', '测试步骤', '预期结果']
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")

            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment

            # 写入数据
            row = 2
            for case_set in case_sets:
                try:
                    test_cases = json.loads(case_set.test_cases) if case_set.test_cases else []
                except:
                    test_cases = []

                if not test_cases:
                    # 如果没有测试用例，也要显示用例集名称
                    ws.cell(row=row, column=1, value=case_set.name)
                    row += 1
                else:
                    for test_case in test_cases:
                        ws.cell(row=row, column=1, value=case_set.name)
                        ws.cell(row=row, column=2, value=test_case.get('name', ''))
                        ws.cell(row=row, column=3, value=test_case.get('precondition', ''))
                        ws.cell(row=row, column=4, value=test_case.get('test_steps', ''))
                        ws.cell(row=row, column=5, value=test_case.get('expected_result', ''))
                        row += 1

            # 设置列宽
            column_widths = [20, 25, 30, 40, 30]
            for i, width in enumerate(column_widths, 1):
                ws.column_dimensions[openpyxl.utils.get_column_letter(i)].width = width

            # 保存到内存
            output = BytesIO()
            wb.save(output)
            output.seek(0)
            file_content = output.getvalue()
            output.close()

            # 生成文件名
            from libs.utils import human_datetime
            timestamp = human_datetime().replace(" ", "_").replace(":", "-")
            filename = f'测试用例集导出_{timestamp}.xlsx'

            # 创建响应
            response = HttpResponse(file_content)
            response['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            encoded_filename = urllib.parse.quote(filename)
            response['Content-Disposition'] = f'attachment; filename="{filename}"; filename*=UTF-8\'\'{encoded_filename}'
            response['Content-Length'] = len(file_content)

            return response

        except Exception as e:
            return json_response(error=f'导出失败: {str(e)}')

    @auth('exec.task.do')
    def post(self, request):
        """从Excel导入测试用例集"""
        file = request.FILES.get('file')

        if not file:
            return json_response(error="未提供文件", status_code=400)

        try:
            import openpyxl
            from libs.utils import human_datetime

            workbook = openpyxl.load_workbook(file)
            sheet = workbook.active

            # 获取表头
            header = [cell.value for cell in sheet[1]]

            # 检查必须的列
            required_columns = ['用例名称', '测试步骤', '预期结果']
            missing_columns = []
            column_mapping = {}

            for col in required_columns:
                try:
                    column_mapping[col] = header.index(col)
                except ValueError:
                    missing_columns.append(col)

            if missing_columns:
                return json_response(
                    error=f"Excel文件必须包含以下列: {', '.join(missing_columns)}",
                    status_code=400
                )

            # 可选列的映射
            optional_columns = {
                '预置条件': 'precondition'
            }

            for col, field in optional_columns.items():
                try:
                    column_mapping[col] = header.index(col)
                except ValueError:
                    pass  # 可选列不存在也没关系

            # 解析数据
            test_cases = []
            errors = []

            for row_num in range(2, sheet.max_row + 1):
                row = sheet[row_num]

                try:
                    # 检查是否为空行
                    if not any(cell.value for cell in row):
                        continue

                    # 获取必填字段
                    case_name = row[column_mapping['用例名称']].value
                    test_steps = row[column_mapping['测试步骤']].value
                    expected_result = row[column_mapping['预期结果']].value

                    if not case_name or not test_steps or not expected_result:
                        errors.append(f"第{row_num}行: 用例名称、测试步骤、预期结果为必填项")
                        continue

                    # 构建测试用例数据
                    test_case = {
                        'id': len(test_cases) + 1,
                        'name': str(case_name).strip(),
                        'test_steps': str(test_steps).strip(),
                        'expected_result': str(expected_result).strip(),
                        'enabled': True,
                        'sort_order': len(test_cases) + 1
                    }

                    # 处理可选字段
                    if '预置条件' in column_mapping:
                        precondition = row[column_mapping['预置条件']].value
                        test_case['precondition'] = str(precondition).strip() if precondition else ''
                    else:
                        test_case['precondition'] = ''

                    test_cases.append(test_case)

                except Exception as e:
                    errors.append(f"第{row_num}行解析错误: {str(e)}")

            if not test_cases:
                return json_response(
                    error="Excel文件中没有有效的测试用例数据" + (f"，错误信息: {'; '.join(errors)}" if errors else ""),
                    status_code=400
                )

            # 创建测试用例集
            timestamp = human_datetime()
            case_set_name = f"导入用例集{timestamp}"

            new_case_set = TestCaseSet(
                name=case_set_name,
                description=f"从Excel导入的测试用例集，导入时间: {timestamp}",
                category='导入',
                test_cases=json.dumps(test_cases, ensure_ascii=False),
                created_by_id=1,
                created_at=timestamp,
                updated_at=timestamp
            )

            new_case_set.save()

            # 构建返回消息
            message = f"导入完成: 创建测试用例集 '{case_set_name}'，包含 {len(test_cases)} 个测试用例"

            if errors:
                message += f"，但有 {len(errors)} 个错误"

            return json_response({
                'message': message,
                'case_set_id': new_case_set.id,
                'case_set_name': case_set_name,
                'test_cases_count': len(test_cases),
                'errors': errors[:10] if errors else []  # 只返回前10个错误
            })

        except Exception as e:
            return json_response(error=f"处理Excel文件失败: {e}", status_code=500)


class RemoteLogFetchView(View):
    """
    远程日志获取视图
    通过SSH连接到远程服务器获取指定路径的日志文件
    """

    @auth('exec.task.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('log_path', help='请输入日志文件路径'),
            Argument('plan_name', help='请输入测试计划名称'),
            Argument('host_id', type=int, help='请选择目标主机'),
            Argument('server_name', required=False, help='远程服务器名称（兼容旧版本）')
        ).parse(request.body)

        if error:
            return json_response(error=error)

        try:
            # 使用主机选择器获取日志内容
            log_content = self._fetch_remote_log_from_host(form.log_path, form.host_id)

            # 获取主机信息用于返回
            host = Host.objects.get(pk=form.host_id)

            return json_response({
                'success': True,
                'content': log_content,
                'plan_name': form.plan_name,
                'log_path': form.log_path,
                'host_info': {
                    'id': host.id,
                    'name': host.name,
                    'hostname': host.hostname
                }
            })

        except Exception as e:
            return json_response(error=f"获取远程日志失败: {str(e)}")

    def _fetch_remote_log_from_host(self, log_path, host_id):
        """
        通过指定主机的SSH连接获取远程日志文件内容
        """
        try:
            # 获取主机对象
            host = Host.objects.get(pk=host_id)

            # 使用主机的SSH连接
            with host.get_ssh() as ssh:
                # 执行cat命令读取日志文件
                command = f"cat {log_path}"
                exit_code, content = ssh.exec_command_raw(command)

                if exit_code != 0:
                    raise Exception(f"读取日志文件失败 (退出码: {exit_code}): {content}")

                if not content.strip():
                    raise Exception("日志文件为空或不存在")

                return content

        except Host.DoesNotExist:
            raise Exception(f"主机不存在 (ID: {host_id})")
        except Exception as e:
            raise Exception(f"SSH连接或文件读取失败: {str(e)}")

    def _fetch_remote_log(self, log_path, server_name='mcp1'):
        """
        通过SSH连接获取远程日志文件内容
        """
        import paramiko
        import io
        
        try:
            # 这里应该从配置或数据库中获取SSH连接信息
            # 暂时使用硬编码的连接信息进行演示
            ssh_config = self._get_ssh_config(server_name)
            
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 建立SSH连接
            ssh.connect(
                hostname=ssh_config['hostname'],
                port=ssh_config.get('port', 22),
                username=ssh_config['username'],
                password=ssh_config.get('password'),
                key_filename=ssh_config.get('key_filename'),
                timeout=30
            )
            
            # 执行cat命令读取日志文件
            command = f"cat {log_path}"
            stdin, stdout, stderr = ssh.exec_command(command)
            
            # 读取输出
            content = stdout.read().decode('utf-8', errors='ignore')
            error_output = stderr.read().decode('utf-8', errors='ignore')
            
            ssh.close()
            
            if error_output and not content:
                raise Exception(f"读取日志文件失败: {error_output}")
            
            return content
            
        except Exception as e:
            raise Exception(f"SSH连接或读取文件失败: {str(e)}")
    
    def _get_ssh_config(self, server_name):
        """
        获取SSH连接配置
        在实际应用中，这应该从数据库或配置文件中获取
        """
        # 尝试从Host模型中获取SSH配置
        try:
            from apps.host.models import Host
            host = Host.objects.filter(name=server_name).first()
            if host:
                return {
                    'hostname': host.hostname,
                    'username': host.username,
                    'password': host.password if hasattr(host, 'password') else None,
                    'key_filename': None,  # 可以根据需要配置SSH密钥
                    'port': host.port
                }
        except:
            pass
        
        # 如果没有找到Host记录，使用MCP服务器的配置
        # 这里根据实际的MCP服务器配置进行调整
        configs = {
            'mcp1': {
                'hostname': '127.0.0.1',  # 替换为实际的MCP服务器IP
                'username': 'admin',      # 替换为实际的用户名
                'password': 'admin',      # 替换为实际的密码
                'key_filename': None,     # 如果使用SSH密钥，指定密钥文件路径
                'port': 22
            }
        }
        
        if server_name not in configs:
            raise Exception(f"未找到服务器 {server_name} 的SSH配置")
        
        return configs[server_name]

