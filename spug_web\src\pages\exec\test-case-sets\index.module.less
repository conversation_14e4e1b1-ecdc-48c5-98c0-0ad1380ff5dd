.testCaseSetsPage {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.caseSetCard {
  height: 280px;
  transition: all 0.3s ease;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  .ant-card-body {
    height: 200px;
    overflow: hidden;
  }
  
  .ant-card-actions {
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
    
    li {
      margin: 8px 0;
      
      .anticon {
        font-size: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: scale(1.2);
        }
      }
    }
  }
}

.selectedCard {
  border: 2px solid #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.cardTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  font-size: 16px;
  color: #262626;
  margin-bottom: 8px;
}

.cardContent {
  .description {
    color: #666;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 36px;
  }
  
  .caseCount {
    margin-bottom: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    
    .ant-tag {
      margin: 0;
      font-size: 11px;
      padding: 2px 6px;
      border-radius: 4px;
    }
  }
  
  .casePreview {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 8px;
    
    .ant-tag {
      margin: 0;
      font-size: 10px;
      padding: 1px 4px;
      border-radius: 3px;
      max-width: 80px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .testCaseSetsPage {
    padding: 16px;
  }
  
  .caseSetCard {
    height: auto;
    min-height: 240px;
  }
}

// 空状态样式
.ant-empty {
  margin: 40px 0;
  
  .ant-empty-description {
    color: #999;
    font-size: 14px;
  }
}

// 抽屉样式优化
.ant-drawer-header {
  border-bottom: 1px solid #f0f0f0;
  
  .ant-drawer-title {
    font-weight: 600;
  }
}

.ant-drawer-body {
  .ant-form-item-label > label {
    font-weight: 500;
    color: #262626;
  }
  
  .ant-input,
  .ant-input:focus,
  .ant-input-focused {
    border-color: #d9d9d9;
    box-shadow: none;
    
    &:hover {
      border-color: #40a9ff;
    }
    
    &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}
