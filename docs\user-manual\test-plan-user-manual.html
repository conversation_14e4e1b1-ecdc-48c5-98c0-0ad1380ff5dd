<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试计划与任务执行 - 用户手册</title>
    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 复制 model-storage-user-manual.html 中的样式 -->
    <style>
        /* --- 基础重置 --- */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #2c3e50; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }

        /* --- 头部 --- */
        .header { background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%); color: white; padding: 60px 0; text-align: center; position: relative; overflow: hidden; }
        .header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>'); opacity: 0.3; }
        .header-content { position: relative; z-index: 1; }
        .header h1 { font-size: 3.5rem; margin-bottom: 20px; font-weight: 300; }
        .header p { font-size: 1.3rem; opacity: 0.9; max-width: 600px; margin: 0 auto; }
        .version-badge { display: inline-block; background: rgba(255, 255, 255, 0.2); padding: 8px 16px; border-radius: 20px; font-size: 0.9rem; margin-top: 20px; }

        /* --- 导航栏 --- */
        .nav { background: white; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); position: sticky; top: 0; z-index: 100; }
        .nav-content { display: flex; justify-content: space-between; align-items: center; padding: 15px 0; }
        .nav-links { display: flex; list-style: none; gap: 30px; }
        .nav-links a { text-decoration: none; color: #2c3e50; font-weight: 500; transition: color 0.3s; }
        .nav-links a:hover { color: #06b6d4; }

        /* --- 主要内容 --- */
        .main-content { padding: 60px 0; }
        .section { margin-bottom: 80px; }
        .section-title { font-size: 2.5rem; margin-bottom: 20px; color: #2c3e50; text-align: center; }
        .section-subtitle { font-size: 1.2rem; color: #7f8c8d; text-align: center; margin-bottom: 50px; }

        /* --- 步骤卡片 --- */
        .steps { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-top: 40px; }
        .step { background: white; border-radius: 15px; padding: 30px; text-align: center; position: relative; border: 2px solid #e9ecef; }
        .step-number { position: absolute; top: -15px; left: 50%; transform: translateX(-50%); background: #06b6d4; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; }
        .step-title { font-size: 1.3rem; margin-bottom: 15px; color: #2c3e50; }
        .step-description { color: #7f8c8d; }

        /* --- 截图样式 --- */
        .screenshot { background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 30px 0; text-align: center; border: 2px dashed #dee2e6; }
        .screenshot img { max-width: 100%; border-radius: 8px; }
        .screenshot-caption { color: #6c757d; font-style: italic; margin-top: 8px; }

        /* --- 代码块 --- */
        .code-block { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 8px; font-family: 'Courier New', monospace; margin: 20px 0; overflow-x: auto; }

        /* --- 提示框 --- */
        .tip, .warning, .info { padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid; }
        .tip { background: #f0f9ff; border-color: #0ea5e9; color: #0c4a6e; }
        .warning { background: #fef3c7; border-color: #f59e0b; color: #92400e; }
        .info { background: #ecfdf5; border-color: #10b981; color: #065f46; }

        /* --- 响应式 --- */
        @media (max-width: 768px) {
            .header h1 { font-size: 2.5rem; }
            .nav-links { display: none; }
            .steps { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1><i class="fas fa-calendar-check"></i> 测试计划与任务执行</h1>
                <p>完整的从计划创建到结果展示全流程指南</p>
                <div class="version-badge">v1.0 用户手册</div>
            </div>
        </div>
    </header>

    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container">
            <div class="nav-content">
                <div class="logo"><strong>目录</strong></div>
                <ul class="nav-links">
                    <li><a href="#plan-create">创建测试计划</a></li>
                    <li><a href="#plan-add-steps">新增步骤</a></li>
                    <li><a href="#plan-import-cmd">引入命令</a></li>
                    <li><a href="#plan-order">调整顺序</a></li>
                    <li><a href="#plan-vars">变量配置</a></li>
                    <li><a href="#task-exec">任务执行</a></li>
                    <li><a href="#exec-records">查看记录</a></li>
                    <li><a href="#smart-extract">智能提取</a></li>
                    <li><a href="#result-display">结果展示</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 创建测试计划 -->
            <section id="plan-create" class="section">
                <h2 class="section-title">1. 创建测试计划</h2>
                <p class="section-subtitle">在开始之前，需要先创建一个新的测试计划。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">进入测试计划页面</h3>
                        <p class="step-description">点击系统菜单中的 <strong>发布计划 &gt; 测试计划</strong>，进入列表页。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">点击“新建测试计划”</h3>
                        <p class="step-description">右上角 <strong>新建</strong> 按钮，用于创建新的测试计划。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">填写计划信息</h3>
                        <p class="step-description">输入计划名称、描述、起止时间等基本信息。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <h3 class="step-title">保存</h3>
                        <p class="step-description">点击 <strong>保存</strong>，测试计划即创建完成。</p>
                    </div>
                </div>

                <!-- 截图 -->
                <div class="screenshot">
                    <img src="测试计划.png" alt="测试计划列表界面">
                    <p class="screenshot-caption">测试计划列表界面</p>
                </div>
                <div class="screenshot">
                    <img src="测试计划-编辑页面.png" alt="测试计划编辑界面">
                    <p class="screenshot-caption">测试计划编辑界面</p>
                </div>

                <div class="tip"><strong>💡 小贴士：</strong> 建议使用统一命名规范，如 <code>项目-版本-日期</code>，便于检索。</div>
            </section>

            <!-- 新增步骤 -->
            <section id="plan-add-steps" class="section">
                <h2 class="section-title">2. 新增步骤</h2>
                <p class="section-subtitle">在测试计划中添加具体的测试步骤。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">点击“新增步骤”</h3>
                        <p class="step-description">在计划详情页，找到步骤列表，点击 <strong>新增步骤</strong>。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">填写步骤信息</h3>
                        <p class="step-description">输入步骤名称、执行人、预估时间等。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">保存步骤</h3>
                        <p class="step-description">点击 <strong>保存</strong> 将步骤添加到计划中。</p>
                    </div>
                </div>
            </section>

            <!-- 引入命令 -->
            <section id="plan-import-cmd" class="section">
                <h2 class="section-title">3. 引入命令</h2>
                <p class="section-subtitle">通过预定义命令模板，快速生成标准化步骤。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">打开“引入命令”对话框</h3>
                        <p class="step-description">在步骤区域点击 <strong>引入命令</strong> 按钮。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">选择命令模板</h3>
                        <p class="step-description">从下拉列表中选择合适的命令模板。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">确认引入</h3>
                        <p class="step-description">点击 <strong>确定</strong>，系统自动将命令解析为步骤条目。</p>
                    </div>
                </div>

                <div class="screenshot">
                    <img src="引入命令.png" alt="引入命令界面">
                    <p class="screenshot-caption">引入命令界面</p>
                </div>

                <div class="info"><strong>✅ 提示：</strong> 使用命令模板可减少重复录入，提高效率。</div>
            </section>

            <!-- 调整顺序 -->
            <section id="plan-order" class="section">
                <h2 class="section-title">4. 调整步骤顺序</h2>
                <p class="section-subtitle">通过拖拽或快捷按钮调整步骤执行顺序。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">拖拽步骤</h3>
                        <p class="step-description">按住步骤左侧拖拽图标，将其移动到目标位置。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">使用上下移动按钮</h3>
                        <p class="step-description">点击 <i class="fas fa-arrow-up"></i> / <i class="fas fa-arrow-down"></i> 图标快速调整。</p>
                    </div>
                </div>

                <!-- TODO 图片占位，尚未提供调整顺序截图 -->
                <div class="screenshot">
                    <div class="screenshot-placeholder">
                        <i class="fas fa-image" style="font-size: 3rem; color: #dee2e6;"></i>
                        <span style="margin-left: 15px;">TODO: 调整顺序界面截图</span>
                    </div>
                    <p class="screenshot-caption">请补充调整顺序界面截图</p>
                </div>
            </section>

            <!-- 变量配置 -->
            <section id="plan-vars" class="section">
                <h2 class="section-title">5. 变量配置</h2>
                <p class="section-subtitle">为步骤配置动态变量，提高脚本灵活性。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">打开变量配置面板</h3>
                        <p class="step-description">点击步骤右侧 <i class="fas fa-cog"></i> 图标进入变量配置。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">添加变量</h3>
                        <p class="step-description">填写变量名、默认值及说明。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3 class="step-title">保存变量</h3>
                        <p class="step-description">点击 <strong>保存</strong>，变量即可在执行时被替换。</p>
                    </div>
                </div>

                <div class="screenshot">
                    <img src="测试计划变量配置.png" alt="变量配置界面">
                    <p class="screenshot-caption">变量配置界面</p>
                </div>
            </section>

            <!-- 任务执行 -->
            <section id="task-exec" class="section">
                <h2 class="section-title">6. 任务执行</h2>
                <p class="section-subtitle">启动测试计划，系统自动按顺序执行步骤。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">开始执行</h3>
                        <p class="step-description">在计划详情页点击 <strong>开始执行</strong> 按钮。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">实时查看日志</h3>
                        <p class="step-description">执行过程中，可实时查看控制台输出。</p>
                    </div>
                </div>

                <div class="screenshot">
                    <img src="任务执行.png" alt="任务执行界面">
                    <p class="screenshot-caption">任务执行界面</p>
                </div>
            </section>

            <!-- 查看记录 -->
            <section id="exec-records" class="section">
                <h2 class="section-title">7. 查看执行记录</h2>
                <p class="section-subtitle">历史记录方便追溯和审计。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">打开执行记录</h3>
                        <p class="step-description">在计划详情页点击 <strong>执行记录</strong> 标签。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">查看详情</h3>
                        <p class="step-description">点击某条记录，可查看完整日志、变量值及耗时。</p>
                    </div>
                </div>

                <div class="screenshot">
                    <img src="执行记录.png" alt="执行记录界面">
                    <p class="screenshot-caption">执行记录界面</p>
                </div>
            </section>

            <!-- 智能提取结果 -->
            <section id="smart-extract" class="section">
                <h2 class="section-title">8. 智能提取结果</h2>
                <p class="section-subtitle">系统可自动解析日志，提取关键指标。</p>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3 class="step-title">启用智能解析</h3>
                        <p class="step-description">在计划设置中勾选 <strong>启用智能提取</strong>。</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3 class="step-title">配置正则表达式</h3>
                        <p class="step-description">支持 <code>Regex</code> 或 <code>JSONPath</code> 提取。</p>
                    </div>
                </div>

                <!-- TODO 图片占位，尚未提供智能提取截图 -->
                <div class="screenshot">
                    <div class="screenshot-placeholder">
                        <i class="fas fa-image" style="font-size: 3rem; color: #dee2e6;"></i>
                        <span style="margin-left: 15px;">TODO: 智能提取结果界面截图</span>
                    </div>
                    <p class="screenshot-caption">请补充智能提取结果界面截图</p>
                </div>
            </section>

            <!-- 结果展示 -->
            <section id="result-display" class="section">
                <h2 class="section-title">9. 结果展示</h2>
                <p class="section-subtitle">一目了然地查看执行结果与统计。</p>

                <div class="screenshot">
                    <img src="结果展示.png" alt="结果展示界面">
                    <p class="screenshot-caption">结果展示界面</p>
                </div>

                <div class="tip"><strong>🎉 恭喜：</strong> 通过以上步骤，您已完成测试计划的全流程操作！</div>
            </section>

        </div>
    </main>

    <!-- 底部 -->
    <footer class="footer">
        <div class="container">
            &copy; 2024 模型存储系统
        </div>
    </footer>
</body>
</html> 