import React, { useState } from 'react';
import { Modal, Form, Input, message, Space, Tag } from 'antd';
import { UserOutlined, SendOutlined, BellOutlined } from '@ant-design/icons';
import { http } from 'libs';
import UserSelector from 'components/UserSelector';

const { TextArea } = Input;

/**
 * 任务分配弹窗组件
 */
export default function TaskAssignModal({ 
  visible, 
  onCancel, 
  onOk, 
  task,
  loading = false 
}) {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);
      
      // 构建分配数据
      const assignData = {
        task_id: task?.id,
        task_name: task?.model_name,
        assignee: values.assignee,
        message: values.message || '',
        notify_wechat: true, // 预留企业微信通知功能
        assigned_by: '管理员' // 这里可以从用户上下文获取
      };

      // 调用真实的任务分配API
      const response = await http.post('/api/model-storage/tasks/assign/', assignData);

      if (response.error) {
        message.error(response.error);
        return;
      }

      // 显示分配结果和通知状态
      const assignResult = response.data;
      const wechatNotification = assignResult?.assign_log?.wechat_notification;

      let successMessage = response.message || `任务已成功分配给 ${values.assignee}`;

      if (wechatNotification) {
        if (wechatNotification.success) {
          successMessage += '，企业微信通知已发送';
        } else {
          successMessage += `，但企业微信通知发送失败: ${wechatNotification.message}`;
        }
      }

      message.success(successMessage);
      form.resetFields();
      onOk && onOk(assignData);
      
    } catch (error) {
      console.error('任务分配失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('任务分配失败，请重试');
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel && onCancel();
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <SendOutlined style={{ color: '#1890ff' }} />
          <span>任务分配</span>
        </div>
      }
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={submitting || loading}
      okText="分配任务"
      cancelText="取消"
      width={600}
      maskClosable={false}
      destroyOnClose
    >
      <div style={{ marginBottom: '16px', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
          <Tag color="blue">任务信息</Tag>
        </div>
        <div style={{ fontSize: '14px' }}>
          <div><strong>模型名称:</strong> {task?.model_name}</div>
          <div style={{ marginTop: '4px' }}>
            <strong>当前负责人:</strong> 
            <Tag icon={<UserOutlined />} color="green" style={{ marginLeft: '8px' }}>
              {task?.tester}
            </Tag>
          </div>
          <div style={{ marginTop: '4px' }}>
            <strong>任务时间:</strong> {task?.start_date} ~ {task?.end_date}
          </div>
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        preserve={false}
      >
        <Form.Item
          name="assignee"
          label={
            <Space>
              <UserOutlined />
              <span>分配给</span>
            </Space>
          }
          rules={[
            { required: true, message: '请选择要分配的人员' }
          ]}
        >
          <UserSelector 
            placeholder="请输入人员姓名，支持模糊搜索"
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          name="message"
          label={
            <Space>
              <BellOutlined />
              <span>分配说明</span>
            </Space>
          }
        >
          <TextArea
            rows={4}
            placeholder="请输入任务分配的说明信息（可选）&#10;例如：请协助完成该模型的测试工作，注意关注性能指标..."
            maxLength={500}
            showCount
          />
        </Form.Item>

        <div style={{ 
          padding: '12px', 
          backgroundColor: '#e6f7ff', 
          borderRadius: '6px',
          border: '1px solid #91d5ff'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
            <BellOutlined style={{ color: '#1890ff' }} />
            <span style={{ fontWeight: 500, color: '#1890ff' }}>通知设置</span>
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            • 任务分配后将自动发送企业微信通知给被分配人员
            <br />
            • 通知内容包含任务详情和分配说明
            <br />
            • 被分配人员可通过系统查看任务详情
          </div>
        </div>
      </Form>
    </Modal>
  );
}
