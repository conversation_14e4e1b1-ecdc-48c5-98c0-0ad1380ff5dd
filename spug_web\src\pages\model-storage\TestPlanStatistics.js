import React, { useEffect } from 'react';
import { observer } from 'mobx-react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Progress, 
  List, 
  Tag,
  Typography,
  Space,
  Avatar
} from 'antd';
import {
  TrophyOutlined,
  TeamOutlined,
  ThunderboltOutlined,
  WarningOutlined,
  FireOutlined
} from '@ant-design/icons';
import store from './store';
import styles from './index.module.css';

const { Title, Text } = Typography;

const TestPlanStatistics = observer(() => {
  useEffect(() => {
    store.fetchTestPlanStatistics();
  }, []);

  const { overview, personnel, gpu_resources, risk_alerts } = store.testPlanStats;

  const getStatusColor = (status) => {
    const colorMap = {
      'completed': '#52c41a',
      'in_progress': '#1890ff',
      'pending': '#faad14',
      'delayed': '#ff4d4f',
      'blocked': '#f5222d',
      'cancelled': '#8c8c8c'
    };
    return colorMap[status] || '#d9d9d9';
  };

  const getPriorityColor = (priority) => {
    const colorMap = {
      'p1': '#ff4d4f',
      'p2': '#faad14',
      'p3': '#52c41a'
    };
    return colorMap[priority] || '#d9d9d9';
  };

  const getVendorIcon = (vendor) => {
    const iconMap = {
      'NVIDIA': '🔥',
      'AMD': '⚡',
      'KUNLUNXIN': '🚀',
      'Intel': '💎',
    };
    return iconMap[vendor] || '🖥️';
  };



  return (
    <div className={styles.statisticsContainer}>
      {/* 统计卡片横向布局 */}
      <Row gutter={[16, 16]}>
        {/* 总体概况 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card
            className={styles.overviewCard}
            title={
              <Space>
                <TrophyOutlined style={{ color: 'white' }} />
                <span>总体概况</span>
              </Space>
            }
            size="small"
          >
            <Row gutter={[8, 8]}>
              <Col span={12}>
                <Statistic
                  title={<span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>发布计划</span>}
                  value={overview?.total_plans || 0}
                  valueStyle={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title={<span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>测试任务</span>}
                  value={overview?.total_tasks || 0}
                  valueStyle={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}
                />
              </Col>
            </Row>
            <div style={{ textAlign: 'center', marginTop: 16 }}>
              <Progress
                type="circle"
                percent={overview?.completion_rate || 0}
                format={percent => `${percent}%`}
                width={80}
                strokeColor={{
                  '0%': '#64b5f6',
                  '100%': '#81c784',
                }}
                trailColor="rgba(255, 255, 255, 0.3)"
              />
              <div style={{ marginTop: 8 }}>
                <Text strong style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.9)' }}>完成率</Text>
              </div>
            </div>
          </Card>
        </Col>

        {/* 人员效率统计 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card
            className={styles.personnelCard}
            title={
              <Space>
                <TeamOutlined style={{ color: 'white' }} />
                <span>人员效率</span>
              </Space>
            }
            size="small"
          >
            <Statistic
              title={<span style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '14px' }}>测试人员</span>}
              value={personnel?.total_testers || 0}
              valueStyle={{ color: 'white', fontSize: '28px', fontWeight: '700' }}
            />
            <div style={{ marginTop: 20 }}>
              <Text strong style={{ fontSize: '13px', color: 'rgba(255, 255, 255, 0.9)', marginBottom: '8px', display: 'block' }}>
                🏆 效率排行
              </Text>
              <List
                size="small"
                dataSource={personnel?.tester_stats?.slice(0, 3) || []}
                renderItem={(item, index) => (
                  <List.Item style={{ padding: '6px 0', borderBottom: 'none' }}>
                    <Space size="small" style={{ width: '100%', justifyContent: 'space-between' }}>
                      <Space size="small">
                        <Avatar
                          size="small"
                          style={{
                            backgroundColor: index === 0 ? '#ffd700' : index === 1 ? '#c0c0c0' : '#cd7f32',
                            fontSize: '10px',
                            fontWeight: 'bold'
                          }}
                        >
                          {index + 1}
                        </Avatar>
                        <Text style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.95)', fontWeight: '500' }}>
                          {item.tester || '未知'}
                        </Text>
                      </Space>
                      <Tag
                        size="small"
                        style={{
                          background: 'rgba(255, 255, 255, 0.25)',
                          color: 'white',
                          border: 'none',
                          borderRadius: '12px',
                          fontWeight: '600'
                        }}
                      >
                        {item.completed_tasks || 0}
                      </Tag>
                    </Space>
                  </List.Item>
                )}
              />
            </div>
          </Card>
        </Col>

        {/* GPU资源统计 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card
            className={styles.gpuCard}
            title={
              <Space>
                <ThunderboltOutlined style={{ color: '#333' }} />
                <span style={{ color: '#333' }}>GPU资源</span>
              </Space>
            }
            size="small"
          >
            <Statistic
              title={<span style={{ color: '#666' }}>GPU设备</span>}
              value={gpu_resources?.total_devices || 0}
              valueStyle={{ color: '#333', fontSize: '24px', fontWeight: 'bold' }}
            />
            <div style={{ marginTop: 16 }}>
              <Text strong style={{ fontSize: '12px', color: '#666' }}>热门型号</Text>
              <List
                size="small"
                dataSource={gpu_resources?.popular_models?.slice(0, 3) || []}
                renderItem={(item) => (
                  <List.Item style={{ padding: '4px 0' }}>
                    <Space size="small">
                      <FireOutlined style={{ color: '#ff6b35', fontSize: '12px' }} />
                      <Text style={{ fontSize: '12px', color: '#333' }}>{item.gpu_model}</Text>
                      <Tag size="small" style={{ background: '#fff3e0', color: '#e65100', border: '1px solid #ffcc80' }}>
                        {item.usage_count}
                      </Tag>
                    </Space>
                  </List.Item>
                )}
              />
            </div>
          </Card>
        </Col>

        {/* 风险预警统计 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card
            className={styles.riskCard}
            title={
              <Space>
                <WarningOutlined style={{ color: '#333' }} />
                <span style={{ color: '#333' }}>风险预警</span>
              </Space>
            }
            size="small"
          >
            <Row gutter={[8, 8]}>
              <Col span={12}>
                <Statistic
                  title={<span style={{ color: '#666' }}>高优先级</span>}
                  value={risk_alerts?.high_priority_tasks || 0}
                  valueStyle={{ color: '#d32f2f', fontSize: '18px', fontWeight: 'bold' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title={<span style={{ color: '#666' }}>延期任务</span>}
                  value={risk_alerts?.delayed_tasks || 0}
                  valueStyle={{ color: '#f57c00', fontSize: '18px', fontWeight: 'bold' }}
                />
              </Col>
            </Row>
            <div style={{ textAlign: 'center', marginTop: 16 }}>
              <Statistic
                title={<span style={{ color: '#666' }}>风险任务总数</span>}
                value={risk_alerts?.total_risk_tasks || 0}
                valueStyle={{ color: '#7b1fa2', fontSize: '24px', fontWeight: 'bold' }}
              />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
});

export default TestPlanStatistics;
