# 企业微信消息格式设计文档

## 概述

本文档描述了Spug模型测试管理平台中企业微信消息通知的格式设计，包括任务分配、进度更新、任务完成和逾期提醒等场景的消息模板。

## 消息类型

### 1. 任务分配通知

当管理员将测试任务分配给某个用户时，系统会自动发送企业微信通知。

#### 消息格式示例：

```markdown
## 🔥 模型测试任务分配通知

**👋 Hi 张三，您有新的任务分配！**

### 📋 任务概览
**🤖 模型名称：** `Mixtral-8x7B`
**🏷️ 模型类型：** 推理
**💻 GPU型号：** RTX 4090
**⚡ 优先级：** 🔴 P1-高
**📊 当前状态：** ⏳ 待开始
**📈 当前进度：** 0%

### 📅 时间安排
**🚀 开始时间：** 2024-07-11
**🏁 结束时间：** 2024-07-15

### 👨‍💼 分配信息
**分配人：** 管理员
**分配时间：** 2024-07-11 14:30:00

### 💬 分配说明
```
请协助完成该模型的测试工作，注意关注性能指标和稳定性测试。
如有问题请及时沟通。
```

### 🎯 下一步操作
• 🔗 登录 **Spug运维平台** 查看详细信息
• 📞 如有疑问请及时与分配人沟通
• ⏰ 请按时完成任务并及时更新进度
• 📊 完成后请在系统中标记任务状态

---
> 📱 来自 **Spug模型测试管理平台** | 🕐 2024-07-11 14:30:00
```

### 2. 任务进度更新通知

当任务进度发生变化时，系统会发送进度更新通知。

#### 消息格式示例：

```markdown
## 📈 任务进度更新通知

**📊 进展顺利**

### 📋 任务信息
**🤖 模型名称：** `Mixtral-8x7B`
**👤 负责人：** 张三

### 📈 进度详情
**当前进度：** 60%
**进度变化：** +20%
**更新时间：** 2024-07-12 10:15:00

### 📊 进度可视化
`████████████░░░░░░░░ 60%`

### 💬 进度说明
基础功能测试已完成，正在进行性能测试阶段。

---
> 📱 来自 **Spug模型测试管理平台** | 🕐 2024-07-12 10:15:00
```

### 3. 任务完成通知

当任务完成时，系统会发送完成通知。

#### 消息格式示例：

```markdown
## 🎉 任务完成通知

**恭喜！任务已成功完成！**

### 📋 任务信息
**🤖 模型名称：** `Mixtral-8x7B`
**👤 完成人：** 张三
**📅 完成时间：** 2024-07-15 16:30:00
**⏱️ 计划时间：** 2024-07-11 ~ 2024-07-15

### 📊 完成统计
**最终进度：** 100%
**任务状态：** ✅ 已完成

### 📝 完成总结
所有测试项目均已完成，模型性能表现良好，符合预期指标。

### 🎯 后续操作
• 📊 请在系统中确认任务完成状态
• 📄 如需要，请提交相关测试报告
• 🔄 准备接收下一个任务分配

---
> 📱 来自 **Spug模型测试管理平台** | 🕐 2024-07-15 16:30:00
```

### 4. 任务逾期提醒

当任务超过预定完成时间时，系统会发送逾期提醒。

#### 消息格式示例：

```markdown
## ⚠️ 任务逾期提醒

**注意：您有任务已逾期 2 天！**

### 📋 任务信息
**🤖 模型名称：** `Mixtral-8x7B`
**👤 负责人：** 张三
**📅 计划结束：** 2024-07-15
**📈 当前进度：** 75%
**⏰ 逾期天数：** 2 天

### 🚨 紧急操作
• 🔥 请立即处理此任务
• 📞 如有困难请及时沟通
• 📊 请更新任务进度和状态
• ⏰ 如需延期请申请调整计划

---
> 📱 来自 **Spug模型测试管理平台** | 🕐 2024-07-17 09:00:00
```

## 消息设计原则

### 1. 视觉友好
- 使用丰富的emoji图标增强可读性
- 采用markdown格式支持富文本显示
- 合理使用分割线和空行提升视觉层次

### 2. 信息完整
- 包含任务的关键信息（模型名称、负责人、时间等）
- 提供明确的操作指引
- 显示相关的状态和进度信息

### 3. 交互引导
- 明确告知用户下一步应该做什么
- 提供系统访问入口的提示
- 鼓励用户及时沟通和反馈

### 4. 品牌一致性
- 统一的消息来源标识
- 一致的时间格式和显示方式
- 保持与平台整体风格的协调

## 技术实现

### 消息发送流程
1. 触发事件（任务分配、进度更新等）
2. 获取用户企业微信配置
3. 根据消息类型选择对应模板
4. 填充模板数据生成消息内容
5. 调用企业微信API发送消息
6. 记录发送结果和状态

### 配置要求
- 用户需要在系统中配置企业微信机器人Webhook地址
- 支持测试消息发送验证配置正确性
- 提供配置状态管理和批量查看功能

### 错误处理
- 网络异常重试机制
- 发送失败日志记录
- 用户友好的错误提示
- 配置验证和测试功能

## 扩展性

该消息系统设计具有良好的扩展性，可以轻松添加新的消息类型：

1. 在 `message_templates.py` 中添加新的模板方法
2. 在 `wechat_service.py` 中添加对应的发送方法
3. 在业务逻辑中调用相应的通知函数

未来可以考虑添加的消息类型：
- 任务评审通知
- 系统维护通知
- 批量操作结果通知
- 定期报告推送
