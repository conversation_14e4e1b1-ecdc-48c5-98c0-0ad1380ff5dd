/* GPU管理页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundMove 20s ease-in-out infinite;
    z-index: 0;
  }
  
  > * {
    position: relative;
    z-index: 1;
  }
}

@keyframes backgroundMove {
  0%, 100% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(-20px) translateY(-10px);
  }
  50% {
    transform: translateX(20px) translateY(10px);
  }
  75% {
    transform: translateX(-10px) translateY(20px);
  }
}

/* 页面头部样式 */
.header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 32px;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  display: flex;
  align-items: center;
}

/* 统计卡片样式 */
.statsCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
  }
  
  :global(.ant-card-body) {
    padding: 24px;
  }
  
  :global(.ant-statistic-title) {
    color: #666;
    font-weight: 500;
    margin-bottom: 8px;
  }
}

/* 搜索卡片样式 */
.searchCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 24px;
  
  :global(.ant-card-body) {
    padding: 24px;
  }
}

/* 渐变按钮样式 */
.gradientButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  
  &:hover, &:focus {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
}

/* GPU卡片样式 */
.gpuCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
  }
  
  :global(.ant-card-body) {
    padding: 20px;
  }
  
  :global(.ant-card-actions) {
    background: rgba(248, 250, 252, 0.8);
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    
    li {
      margin: 8px 0;
      
      .anticon {
        font-size: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: scale(1.2);
        }
      }
    }
  }
}

.gpuCardHeader {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.gpuIcon {
  font-size: 24px;
  color: #667eea;
  margin-right: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gpuName {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}

.gpuInfo {
  .infoItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .infoLabel {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
  }
}

/* 标签样式 */
.vendorTag {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  padding: 4px 12px;
}

.countTag {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  padding: 4px 12px;
}

.modelTag {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  margin: 2px;
}

.modelList {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.modelPreview {
  cursor: pointer;
  flex: 1;
  
  .modelText {
    color: #374151;
    font-size: 13px;
    margin-top: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.description {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  font-size: 13px;
  color: #6b7280;
  line-height: 1.5;
}

/* 分组视图样式 */
.groupView {
  margin-bottom: 24px;
}

.groupCollapse {
  background: transparent;
  border: none;

  :global(.ant-collapse-item) {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
    margin-bottom: 16px;
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }
  }

  :global(.ant-collapse-header) {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 16px 20px;

    &:hover {
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    }
  }

  :global(.ant-collapse-content) {
    border-top: none;
    background: transparent;
  }

  :global(.ant-collapse-content-box) {
    padding: 20px;
  }
}

.groupHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.groupTitle {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
}

.groupCount {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  padding: 4px 12px;
  font-size: 12px;
}

.emptyGroup {
  text-align: center;
  padding: 40px 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* 分页样式 */
.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.pagination {
  :global(.ant-pagination-item) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;

    &:hover {
      border-color: #667eea;
      transform: translateY(-1px);
    }

    a {
      color: #374151;
      font-weight: 500;
    }
  }

  :global(.ant-pagination-item-active) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;

    a {
      color: white;
    }

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      border-color: #5a6fd8;
    }
  }

  :global(.ant-pagination-prev),
  :global(.ant-pagination-next),
  :global(.ant-pagination-jump-prev),
  :global(.ant-pagination-jump-next) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;

    &:hover {
      border-color: #667eea;
      color: #667eea;
      transform: translateY(-1px);
    }
  }

  :global(.ant-pagination-options) {
    .ant-select-selector {
      border-radius: 8px;
      border: 1px solid #d1d5db;

      &:hover {
        border-color: #667eea;
      }
    }
  }

  :global(.ant-pagination-total-text) {
    color: #6b7280;
    font-weight: 500;
  }
}

/* 空状态样式 */
.emptyContainer {
  text-align: center;
  padding: 60px 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .header {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .searchCard {
    margin-bottom: 16px;
    
    :global(.ant-card-body) {
      padding: 16px;
    }
  }
  
  .gpuCard {
    :global(.ant-card-body) {
      padding: 16px;
    }
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  
  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
}
