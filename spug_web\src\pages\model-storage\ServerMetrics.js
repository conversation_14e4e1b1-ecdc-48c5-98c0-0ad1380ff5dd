import React, { useEffect } from 'react';
import { Card, Row, Col, Progress, Typography } from 'antd';
import { observer } from 'mobx-react';
import store from './store';

const { Text } = Typography;

export default observer(function ServerMetrics({ compact = false }) {
  useEffect(() => {
    store.fetchMetrics();
    const timer = setInterval(() => {
      store.fetchMetrics();
    }, 5000);
    return () => {
      clearInterval(timer);
    };
  }, []);

  const formatNetwork = (value) => {
    if (!value || typeof value !== 'string') return '0 MB/s';
    return value;
  };

  const formatPercent = (value) => {
    if (!value || isNaN(value)) return 0;
    return Math.round(value);
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (compact) {
    return (
      <Card
        size="small"
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ fontSize: '16px', fontWeight: 'bold', color: '#333' }}>服务器监控</span>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {store.metrics.targetHost && (
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {store.metrics.targetHost}
                </Text>
              )}
            </div>
          </div>
        }
        style={{
          marginBottom: 0,
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          border: 'none',
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)'
        }}
      >
        <Row gutter={8}>
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <Progress
                type="circle"
                percent={formatPercent(store.metrics.cpu)}
                format={percent => `${percent}%`}
                width={60}
                status={store.metrics.cpu > 80 ? 'exception' : 'normal'}
              />
              <div style={{ fontSize: '12px', marginTop: 4 }}>CPU</div>
            </div>
          </Col>
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <Progress
                type="circle"
                percent={formatPercent(store.metrics.memory)}
                format={percent => `${percent}%`}
                width={60}
                status={store.metrics.memory > 80 ? 'exception' : 'normal'}
              />
              <div style={{ fontSize: '12px', marginTop: 4 }}>内存</div>
            </div>
          </Col>
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <Progress
                type="circle"
                percent={formatPercent(store.metrics.disk)}
                format={percent => `${percent}%`}
                width={60}
                status={store.metrics.disk > 80 ? 'exception' : 'normal'}
              />
              <div style={{ fontSize: '12px', marginTop: 4 }}>磁盘</div>
            </div>
          </Col>
          <Col span={6}>
            <div style={{ textAlign: 'center', fontSize: '12px' }}>
              <div>网络</div>
              <div style={{ color: '#52c41a' }}>↓ {formatNetwork(store.metrics.networkDownload)}</div>
              <div style={{ color: '#1890ff' }}>↑ {formatNetwork(store.metrics.networkUpload)}</div>
            </div>
          </Col>
        </Row>
      </Card>
    );
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>服务器指标</span>
          <div style={{ fontSize: '14px', fontWeight: 'normal', color: '#666' }}>
            {store.metrics.targetHost && (
              <Text type="secondary">目标主机: {store.metrics.targetHost}</Text>
            )}
            {store.metrics.timestamp && (
              <Text type="secondary" style={{ marginLeft: '16px' }}>
                更新时间: {formatTimestamp(store.metrics.timestamp)}
              </Text>
            )}

          </div>
        </div>
      }
    >
      <Row gutter={16}>
        <Col span={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Progress
                type="dashboard"
                percent={formatPercent(store.metrics.cpu)}
                format={percent => `${percent}%`}
                status={store.metrics.cpu > 80 ? 'exception' : 'normal'}
              />
              <div>CPU 使用率</div>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Progress
                type="dashboard"
                percent={formatPercent(store.metrics.memory)}
                format={percent => `${percent}%`}
                status={store.metrics.memory > 80 ? 'exception' : 'normal'}
              />
              <div>内存使用率</div>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Progress
                type="dashboard"
                percent={formatPercent(store.metrics.disk)}
                format={percent => `${percent}%`}
                status={store.metrics.disk > 80 ? 'exception' : 'normal'}
              />
              <div>HDD_Raid 使用率</div>
            </div>
          </Card>
        </Col>
        {/* <Col span={4}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', marginBottom: '8px', color: '#52c41a' }}>
                ↑ {formatNetwork(store.metrics.networkUpload)}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>上传速度</div>
            </div>
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', marginBottom: '8px', color: '#1890ff' }}>
                ↓ {formatNetwork(store.metrics.networkDownload)}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>下载速度</div>
            </div>
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', marginBottom: '8px', color: '#722ed1' }}>
                {formatNetwork(store.metrics.networkTotal)}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>总流量</div>
            </div>
          </Card>
        </Col> */}
      </Row>
    </Card>
  );
}); 