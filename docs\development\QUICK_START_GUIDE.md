# Spug Platform Quick Start Guide

## Table of Contents

1. [Setup & Installation](#setup--installation)
2. [Basic Usage Examples](#basic-usage-examples)
3. [Common Development Patterns](#common-development-patterns)
4. [API Integration Examples](#api-integration-examples)
5. [Frontend Component Usage](#frontend-component-usage)
6. [Troubleshooting](#troubleshooting)

---

## Setup & Installation

### Backend Setup

1. **Install Dependencies**
```bash
cd spug_api
pip install -r requirements.txt
```

2. **Database Setup**
```bash
python manage.py migrate
python manage.py createsuperuser
```

3. **Start Development Server**
```bash
python manage.py runserver 0.0.0.0:9999
```

### Frontend Setup

1. **Install Dependencies**
```bash
cd spug_web
npm install
```

2. **Configure API Endpoint**
```javascript
// src/setupProxy.js
module.exports = function(app) {
  app.use('/api', proxy('http://localhost:9999'));
};
```

3. **Start Development Server**
```bash
npm start
```

### Environment Configuration

Create `.env` file in project root:
```bash
# Database
DB_NAME=spug.db
DB_ENGINE=sqlite3

# Security
SECRET_KEY=your-secret-key-here
DEBUG=True

# SSH
SSH_PRIVATE_KEY_PATH=/etc/spug/private_key

# Email (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
```

---

## Basic Usage Examples

### 1. Adding a New Host

#### Backend API
```python
# apps/host/views.py
from libs.decorators import auth_required, auth
from libs.parser import JsonParser, Argument
from .models import Host

@auth_required
@auth('host.host.add')
def create_host(request):
    form, error = JsonParser(
        Argument('name', help='Host name is required'),
        Argument('hostname', help='IP address is required'),
        Argument('port', type=int, default=22),
        Argument('username', default='root'),
        Argument('pkey', required=False),
        Argument('desc', required=False)
    ).parse(request.body)
    
    if error:
        return JsonResponse(error, status=400)
    
    # Test SSH connection
    from libs.ssh import SSH
    try:
        ssh = SSH(form.hostname, form.port, form.username, form.pkey)
        ssh.ping()
        form['is_verified'] = True
    except Exception:
        form['is_verified'] = False
    
    host = Host.objects.create(**form, created_by=request.user)
    return JsonResponse(host.to_view())
```

#### Frontend Component
```jsx
// src/pages/host/AddHostForm.jsx
import React, { useState } from 'react';
import { Form, Input, InputNumber, Button, message } from 'antd';
import { ACEditor } from '@/components';
import http from '@/libs/http';

function AddHostForm({ onSuccess }) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const host = await http.post('/api/host/', values);
      message.success('Host added successfully');
      form.resetFields();
      onSuccess && onSuccess(host);
    } catch (error) {
      message.error('Failed to add host');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form form={form} onFinish={handleSubmit} layout="vertical">
      <Form.Item name="name" label="Host Name" rules={[{required: true}]}>
        <Input placeholder="Enter host name" />
      </Form.Item>
      
      <Form.Item name="hostname" label="IP Address" rules={[{required: true}]}>
        <Input placeholder="*************" />
      </Form.Item>
      
      <Form.Item name="port" label="SSH Port" initialValue={22}>
        <InputNumber min={1} max={65535} style={{width: '100%'}} />
      </Form.Item>
      
      <Form.Item name="username" label="Username" initialValue="root">
        <Input />
      </Form.Item>
      
      <Form.Item name="pkey" label="Private Key">
        <ACEditor
          mode="text"
          placeholder="Paste your private key here (optional)"
          height="200px"
        />
      </Form.Item>
      
      <Form.Item name="desc" label="Description">
        <Input.TextArea rows={3} placeholder="Host description" />
      </Form.Item>
      
      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading}>
          Add Host
        </Button>
      </Form.Item>
    </Form>
  );
}

export default AddHostForm;
```

### 2. Executing Commands on Hosts

#### Backend API
```python
# apps/exec/views.py
@auth_required
@auth('exec.task.do')
def execute_task(request):
    form, error = JsonParser(
        Argument('host_ids', type=list, help='Host IDs required'),
        Argument('command', help='Command is required'),
        Argument('run_as', default='root'),
        Argument('timeout', type=int, default=300)
    ).parse(request.body)
    
    if error:
        return JsonResponse(error, status=400)
    
    # Validate hosts
    hosts = Host.objects.filter(id__in=form.host_ids)
    if len(hosts) != len(form.host_ids):
        return JsonResponse({'error': 'Invalid host IDs'}, status=400)
    
    # Execute commands
    results = []
    for host in hosts:
        try:
            with host.get_ssh() as ssh:
                exit_code, output = ssh.exec_command_raw(form.command)
                results.append({
                    'host_id': host.id,
                    'host_name': host.name,
                    'exit_code': exit_code,
                    'output': output,
                    'success': exit_code == 0
                })
        except Exception as e:
            results.append({
                'host_id': host.id,
                'host_name': host.name,
                'exit_code': -1,
                'output': str(e),
                'success': False
            })
    
    return JsonResponse({'results': results})
```

#### Frontend Component
```jsx
// src/pages/exec/CommandExecutor.jsx
import React, { useState } from 'react';
import { Form, Select, Button, Card, message } from 'antd';
import { ACEditor, AuthButton } from '@/components';
import http from '@/libs/http';

function CommandExecutor() {
  const [hosts, setHosts] = useState([]);
  const [command, setCommand] = useState('');
  const [results, setResults] = useState([]);
  const [executing, setExecuting] = useState(false);

  const executeCommand = async () => {
    if (!hosts.length || !command.trim()) {
      message.warning('Please select hosts and enter command');
      return;
    }

    setExecuting(true);
    try {
      const response = await http.post('/api/exec/task/', {
        host_ids: hosts,
        command: command
      });
      
      setResults(response.results);
      message.success('Command executed on all hosts');
    } catch (error) {
      message.error('Execution failed');
    } finally {
      setExecuting(false);
    }
  };

  return (
    <div>
      <Card title="Command Execution">
        <Form layout="vertical">
          <Form.Item label="Target Hosts">
            <Select
              mode="multiple"
              placeholder="Select hosts"
              value={hosts}
              onChange={setHosts}
              // Options would be loaded from API
            />
          </Form.Item>
          
          <Form.Item label="Command">
            <ACEditor
              mode="bash"
              value={command}
              onChange={setCommand}
              height="200px"
              placeholder="Enter command to execute..."
            />
          </Form.Item>
          
          <Form.Item>
            <AuthButton
              auth="exec.task.do"
              type="primary"
              loading={executing}
              onClick={executeCommand}
            >
              Execute Command
            </AuthButton>
          </Form.Item>
        </Form>
      </Card>

      {results.length > 0 && (
        <Card title="Execution Results" style={{marginTop: 16}}>
          {results.map((result, index) => (
            <Card
              key={index}
              size="small"
              title={`${result.host_name} (Exit Code: ${result.exit_code})`}
              style={{
                marginBottom: 8,
                borderColor: result.success ? '#52c41a' : '#ff4d4f'
              }}
            >
              <pre style={{
                backgroundColor: '#f5f5f5',
                padding: 8,
                borderRadius: 4,
                fontSize: 12,
                overflow: 'auto'
              }}>
                {result.output}
              </pre>
            </Card>
          ))}
        </Card>
      )}
    </div>
  );
}

export default CommandExecutor;
```

### 3. Creating a Configuration Manager

#### Backend API
```python
# apps/config/views.py
@auth_required
@auth('config.config.view')
def get_app_config(request):
    app_key = request.GET.get('app')
    env_key = request.GET.get('env')
    format_type = request.GET.get('format', 'json')
    
    if not app_key or not env_key:
        return JsonResponse({'error': 'App and environment required'}, status=400)
    
    app = App.objects.filter(key=app_key).first()
    env = Environment.objects.filter(key=env_key).first()
    
    if not app or not env:
        return JsonResponse({'error': 'App or environment not found'}, status=404)
    
    # Get configurations
    configs = get_app_configs(app, env)
    
    if format_type == 'env':
        # Return as environment file format
        content = '\n'.join([f'{k}={v}' for k, v in configs.items()])
        return HttpResponse(content, content_type='text/plain')
    
    return JsonResponse(configs)

@auth_required
@auth('config.config.edit')
def update_config(request):
    form, error = JsonParser(
        Argument('app_id', type=int),
        Argument('env_id', type=int),
        Argument('key', help='Config key required'),
        Argument('value', help='Config value required'),
        Argument('is_public', type=bool, default=True)
    ).parse(request.body)
    
    if error:
        return JsonResponse(error, status=400)
    
    config, created = Config.objects.update_or_create(
        app_id=form.app_id,
        env_id=form.env_id,
        key=form.key,
        defaults={
            'value': form.value,
            'is_public': form.is_public,
            'updated_by': request.user
        }
    )
    
    return JsonResponse({
        'id': config.id,
        'created': created,
        'message': 'Configuration updated successfully'
    })
```

---

## Common Development Patterns

### 1. API View Pattern

```python
# Standard API view pattern
from libs.decorators import auth_required, auth
from libs.parser import JsonParser, Argument
from libs.utils import json_response

@auth_required
@auth('module.model.action')
def api_view(request):
    # 1. Parse and validate input
    form, error = JsonParser(
        Argument('required_field', help='Field is required'),
        Argument('optional_field', required=False, default='default_value'),
        Argument('typed_field', type=int, default=0)
    ).parse(request.body)
    
    if error:
        return json_response(error=error, status=400)
    
    try:
        # 2. Business logic
        result = perform_operation(form)
        
        # 3. Return success response
        return json_response(data=result)
        
    except ValidationError as e:
        return json_response(error=str(e), status=400)
    except Exception as e:
        logger.exception('Unexpected error')
        return json_response(error='Internal server error', status=500)
```

### 2. React Component Pattern

```jsx
// Standard React component pattern
import React, { useState, useEffect } from 'react';
import { Card, Button, message } from 'antd';
import { TableCard, AuthButton } from '@/components';
import http from '@/libs/http';

function MyComponent() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const result = await http.get('/api/my-endpoint/');
      setData(result);
    } catch (error) {
      message.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async (item) => {
    try {
      await http.post(`/api/my-endpoint/${item.id}/action/`);
      message.success('Action completed');
      fetchData(); // Refresh data
    } catch (error) {
      message.error('Action failed');
    }
  };

  const columns = [
    { title: 'Name', dataIndex: 'name', key: 'name' },
    { title: 'Status', dataIndex: 'status', key: 'status' },
    { 
      title: 'Actions', 
      key: 'actions',
      render: (_, record) => (
        <AuthButton 
          auth="module.model.action"
          size="small"
          onClick={() => handleAction(record)}
        >
          Action
        </AuthButton>
      )
    }
  ];

  return (
    <TableCard
      title="My Data"
      columns={columns}
      dataSource={data}
      loading={loading}
      rowKey="id"
      onReload={fetchData}
      actions={[
        <AuthButton key="add" auth="module.model.add" type="primary">
          Add New
        </AuthButton>
      ]}
    />
  );
}

export default MyComponent;
```

### 3. SSH Operation Pattern

```python
# SSH operation with error handling
from libs.ssh import SSH

def execute_on_hosts(host_ids, command, user_id):
    results = []
    hosts = Host.objects.filter(id__in=host_ids)
    
    for host in hosts:
        result = {
            'host_id': host.id,
            'host_name': host.name,
            'success': False,
            'output': '',
            'error': None
        }
        
        try:
            with host.get_ssh() as ssh:
                exit_code, output = ssh.exec_command_raw(command)
                result.update({
                    'success': exit_code == 0,
                    'exit_code': exit_code,
                    'output': output
                })
                
        except Exception as e:
            result['error'] = str(e)
            
        results.append(result)
    
    # Log operation
    ExecutionLog.objects.create(
        command=command,
        host_count=len(hosts),
        success_count=sum(1 for r in results if r['success']),
        executed_by_id=user_id
    )
    
    return results
```

---

## API Integration Examples

### 1. External API Integration

```python
# Webhook handler for external integrations
@csrf_exempt
@require_http_methods(['POST'])
def webhook_handler(request):
    # Validate webhook signature
    signature = request.headers.get('X-Signature')
    if not validate_webhook_signature(request.body, signature):
        return JsonResponse({'error': 'Invalid signature'}, status=401)
    
    try:
        payload = json.loads(request.body)
        event_type = payload.get('event_type')
        
        if event_type == 'deployment_requested':
            handle_deployment_request(payload)
        elif event_type == 'health_check_failed':
            handle_health_check_failure(payload)
        
        return JsonResponse({'status': 'processed'})
        
    except Exception as e:
        logger.exception('Webhook processing failed')
        return JsonResponse({'error': 'Processing failed'}, status=500)

def handle_deployment_request(payload):
    """Handle deployment request from external system"""
    app_name = payload['app_name']
    version = payload['version']
    environment = payload['environment']
    
    app = App.objects.filter(name=app_name).first()
    if not app:
        raise ValueError(f'App {app_name} not found')
    
    # Trigger deployment
    deployment = trigger_deployment(
        app=app,
        version=version,
        environment=environment,
        triggered_by='webhook'
    )
    
    # Send notification
    send_notification(
        title=f'Deployment Started: {app_name}',
        message=f'Version {version} deployment to {environment} started',
        recipients=app.notification_recipients
    )
```

### 2. Configuration API for External Services

```python
# Public API for external services to fetch configuration
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

@method_decorator(csrf_exempt, name='dispatch')
class ConfigAPIView(View):
    def get(self, request):
        # Authenticate using API key
        api_key = request.GET.get('api_key')
        app_key = request.GET.get('app')
        env_key = request.GET.get('env')
        
        if not authenticate_api_key(api_key):
            return JsonResponse({'error': 'Invalid API key'}, status=401)
        
        if not app_key or not env_key:
            return JsonResponse({'error': 'app and env parameters required'}, status=400)
        
        try:
            configs = get_app_configs(app_key, env_key)
            return JsonResponse(configs)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=404)

# URL configuration
urlpatterns = [
    path('api/v1/config/', ConfigAPIView.as_view(), name='config_api'),
]
```

---

## Frontend Component Usage

### 1. Using TableCard with Custom Features

```jsx
import React, { useState } from 'react';
import { TableCard, AuthButton } from '@/components';
import { Tag, Space, Popconfirm } from 'antd';

function AdvancedHostList() {
  const [hosts, setHosts] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);

  const columns = [
    { 
      title: 'Name', 
      dataIndex: 'name', 
      key: 'name',
      sorter: true,
      filterable: true 
    },
    { 
      title: 'Status', 
      dataIndex: 'is_verified', 
      key: 'status',
      render: (verified) => (
        <Tag color={verified ? 'green' : 'red'}>
          {verified ? 'Verified' : 'Unverified'}
        </Tag>
      ),
      filters: [
        { text: 'Verified', value: true },
        { text: 'Unverified', value: false }
      ]
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <AuthButton auth="host.host.edit" size="small">
            Edit
          </AuthButton>
          <AuthButton auth="host.host.delete" size="small" danger>
            <Popconfirm
              title="Delete this host?"
              onConfirm={() => handleDelete(record)}
            >
              Delete
            </Popconfirm>
          </AuthButton>
        </Space>
      )
    }
  ];

  const batchActions = [
    <AuthButton 
      key="verify" 
      auth="host.host.edit"
      onClick={() => handleBatchVerify(selectedKeys)}
    >
      Verify Selected ({selectedKeys.length})
    </AuthButton>,
    <AuthButton 
      key="delete" 
      auth="host.host.delete"
      danger
      onClick={() => handleBatchDelete(selectedKeys)}
    >
      Delete Selected
    </AuthButton>
  ];

  return (
    <TableCard
      title="Host Management"
      columns={columns}
      dataSource={hosts}
      rowKey="id"
      tKey="host-list" // Persist column preferences
      rowSelection={{
        selectedRowKeys: selectedKeys,
        onChange: setSelectedKeys
      }}
      selected={selectedKeys}
      batchActions={batchActions}
      actions={[
        <TableCard.Search
          key="search"
          keys={['name/Name', 'hostname/IP']}
          onChange={handleSearch}
        />,
        <AuthButton key="add" auth="host.host.add" type="primary">
          Add Host
        </AuthButton>
      ]}
      pagination={{
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => 
          `${range[0]}-${range[1]} of ${total} items`
      }}
    />
  );
}
```

### 2. File Upload with Progress

```jsx
import React, { useState } from 'react';
import { DragUpload } from '@/components';
import { Card, Progress, message } from 'antd';
import http from '@/libs/http';

function ScriptUploader() {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleUpload = async (files) => {
    setUploading(true);
    setProgress(0);

    try {
      const formData = new FormData();
      files.forEach(file => formData.append('scripts', file));

      // Simulate progress (in real app, use upload progress callback)
      const progressInterval = setInterval(() => {
        setProgress(prev => prev < 90 ? prev + 10 : prev);
      }, 200);

      const response = await http.post('/api/scripts/upload/', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      clearInterval(progressInterval);
      setProgress(100);
      
      message.success(`Uploaded ${files.length} scripts successfully`);
      
      // Reset after a delay
      setTimeout(() => {
        setProgress(0);
        setUploading(false);
      }, 1000);

    } catch (error) {
      setUploading(false);
      setProgress(0);
      message.error('Upload failed: ' + error.message);
    }
  };

  return (
    <Card title="Script Upload">
      <DragUpload
        onUpload={handleUpload}
        accept=".sh,.py,.js"
        maxSize={10}
        multiple={true}
        disabled={uploading}
      >
        <div style={{ textAlign: 'center', padding: 40 }}>
          {uploading ? (
            <div>
              <Progress percent={progress} status="active" />
              <p>Uploading scripts...</p>
            </div>
          ) : (
            <div>
              <p>Click or drag script files here to upload</p>
              <p style={{ color: '#999' }}>
                Supports .sh, .py, .js files up to 10MB each
              </p>
            </div>
          )}
        </div>
      </DragUpload>
    </Card>
  );
}
```

---

## Troubleshooting

### Common Issues

#### 1. SSH Connection Problems

```python
# Debug SSH connections
def debug_ssh_connection(hostname, port, username, pkey):
    try:
        ssh = SSH(hostname, port, username, pkey)
        client = ssh.get_client()
        print(f"✓ Connected to {hostname}:{port}")
        
        # Test command execution
        exit_code, output = ssh.exec_command_raw('whoami')
        print(f"✓ Command executed: {output.strip()}")
        
        return True
        
    except Exception as e:
        print(f"✗ Connection failed: {e}")
        return False

# Usage in view
@auth_required
def test_host_connection(request, host_id):
    host = Host.objects.get(id=host_id)
    success = debug_ssh_connection(
        host.hostname, host.port, host.username, host.private_key
    )
    return JsonResponse({'success': success})
```

#### 2. Permission Issues

```javascript
// Debug permission issues in frontend
import { hasPermission } from '@/libs/functools';

function DebugPermissions({ requiredAuth }) {
  const userPermissions = JSON.parse(localStorage.getItem('permissions') || '[]');
  const isSuper = localStorage.getItem('is_supper') === 'true';
  const hasAccess = hasPermission(requiredAuth);

  return (
    <div style={{ border: '1px solid #ccc', padding: 16, margin: 16 }}>
      <h4>Permission Debug Info</h4>
      <p><strong>Required:</strong> {requiredAuth}</p>
      <p><strong>Is Super User:</strong> {isSuper ? 'Yes' : 'No'}</p>
      <p><strong>Has Access:</strong> {hasAccess ? 'Yes' : 'No'}</p>
      <p><strong>User Permissions:</strong></p>
      <pre>{JSON.stringify(userPermissions, null, 2)}</pre>
    </div>
  );
}

// Usage
<DebugPermissions requiredAuth="host.host.add" />
```

#### 3. API Response Issues

```python
# Debug API responses
import logging

logger = logging.getLogger(__name__)

def debug_api_view(request):
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request path: {request.path}")
    logger.info(f"Request headers: {dict(request.headers)}")
    
    if request.method == 'POST':
        logger.info(f"Request body: {request.body}")
    
    # Your view logic here
    response_data = {'status': 'ok'}
    
    logger.info(f"Response data: {response_data}")
    return JsonResponse(response_data)
```

### Performance Optimization

#### 1. Database Query Optimization

```python
# Optimize database queries
def optimized_host_list(request):
    # Use select_related for foreign keys
    hosts = Host.objects.select_related('created_by')\
                       .prefetch_related('groups')\
                       .all()
    
    # Use only() to limit fields
    hosts = hosts.only('id', 'name', 'hostname', 'is_verified')
    
    return JsonResponse([h.to_view() for h in hosts])
```

#### 2. Frontend Performance

```jsx
// Use React.memo for expensive components
import React, { memo } from 'react';

const ExpensiveComponent = memo(function ExpensiveComponent({ data }) {
  // Expensive calculations here
  return <div>{/* Component content */}</div>;
});

// Use useCallback for event handlers
import { useCallback } from 'react';

function MyComponent() {
  const handleClick = useCallback((id) => {
    // Handle click
  }, []);

  return (
    <div>
      {items.map(item => (
        <ExpensiveComponent 
          key={item.id} 
          data={item} 
          onClick={handleClick}
        />
      ))}
    </div>
  );
}
```

This quick start guide provides the essential patterns and examples needed to develop with the Spug platform efficiently. For detailed API references, see the companion documentation files: `API_DOCUMENTATION.md`, `COMPONENT_REFERENCE.md`, and `BACKEND_LIBRARIES.md`.