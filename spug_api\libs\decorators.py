# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from functools import wraps
from .utils import json_response


def auth(perm_list):
    def decorate(view_func):
        # 处理perm_list为字符串或functools.partial对象的情况
        if hasattr(perm_list, 'split'):
            codes = perm_list.split('|')
        else:
            # 如果perm_list不是字符串（可能是functools.partial对象），使用空列表
            codes = []

        @wraps(view_func)
        def wrapper(*args, **kwargs):
            user = None
            for item in args[:2]:
                if hasattr(item, 'user'):
                    user = item.user
                    break
            if user and user.has_perms(codes):
                return view_func(*args, **kwargs)
            return json_response(error='权限拒绝')

        return wrapper

    return decorate
