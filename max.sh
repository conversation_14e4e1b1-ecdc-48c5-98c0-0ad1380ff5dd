#!/bin/bash


log_file1=search_short.log
log_file2=search_long.log

for ttft1 in $(seq 1000 1000 5000); do
#    echo $ttft1
    python3 Max_Combination_Search.py --ttft-standard $ttft1 --tpot-standard 100 |tee -a $log_file1
    echo "-----------------------------------" |tee -a $log_file1
done


echo "all short_test already completed,log saved in $log_file1"

# echo "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++"


# for ttft2 in $(seq 1000 1000 10000); do
#     python3 Max_Combination_Search2.py --ttft-standard $ttft2 --tpot-standard 100 |tee -a $log_file2
#     echo "-----------------------------------" |tee -a $log_file2
# done
# echo "all long_test already completed,log saved in $log_file2"
