# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from django.views.generic import View
from django.http import HttpResponse, Http404
from django.conf import settings
from django_redis import get_redis_connection
from libs import json_response, JsonParser, Argument, auth
from libs.utils import get_request_real_ip, human_datetime, human_time
from libs.mixins import View as BaseView
from concurrent import futures
from threading import Thread
from functools import partial
import os
import json
import time
import uuid
import shutil
import mimetypes
import tempfile
import threading
from django.http.response import HttpResponseBadRequest
from django.db.models import F
from django.http import FileResponse
from apps.host.models import Host
from apps.account.utils import has_host_perm
from apps.file.utils import FileResponseAfter, fetch_dir_list
from apps.file.models import RemoteFolder
from apps.file.remote_manager import RemoteFileManager
import re
import logging

# 定义method_route装饰器（如果导入失败）
def method_route(methods):
    def decorator(func):
        def inner(request, *args, **kwargs):
            if request.method not in methods:
                return json_response(error=f'Method {request.method} not allowed')
            return func(request, *args, **kwargs)
        return inner
    return decorator


class FileView(View):
    """SSH主机文件管理 (原有功能)"""
    @auth('host.console.list')
    def get(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='参数错误'),
            Argument('path', help='参数错误')
        ).parse(request.GET)
        if error is None:
            if not has_host_perm(request.user, form.id):
                return json_response(error='无权访问主机，请联系管理员')
            host = Host.objects.get(pk=form.id)
            if not host:
                return json_response(error='未找到指定主机')
            objects = fetch_dir_list(host, form.path)
            return json_response(objects)
        return json_response(error=error)


class ObjectView(View):
    """SSH主机文件对象操作 (原有功能)"""
    @auth('host.console.list')
    def get(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='参数错误'),
            Argument('file', help='请输入文件路径')
        ).parse(request.GET)
        if error is None:
            if not has_host_perm(request.user, form.id):
                return json_response(error='无权访问主机，请联系管理员')
            host = Host.objects.filter(pk=form.id).first()
            if not host:
                return json_response(error='未找到指定主机')
            filename = os.path.basename(form.file)
            ssh_cli = host.get_ssh().get_client()
            sftp = ssh_cli.open_sftp()
            f = sftp.open(form.file)
            return FileResponseAfter(ssh_cli.close, f, as_attachment=True, filename=filename)
        return json_response(error=error)

    @auth('host.console.upload')
    def post(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='参数错误'),
            Argument('token', help='参数错误'),
            Argument('path', help='参数错误'),
        ).parse(request.POST)
        if error is None:
            if not has_host_perm(request.user, form.id):
                return json_response(error='无权访问主机，请联系管理员')
            file = request.FILES.get('file')
            if not file:
                return json_response(error='请选择要上传的文件')
            host = Host.objects.get(pk=form.id)
            if not host:
                return json_response(error='未找到指定主机')
            rds_cli = get_redis_connection()
            callback = partial(self._compute_progress, rds_cli, form.token, file.size)
            with host.get_ssh() as ssh:
                ssh.put_file_by_fl(file, f'{form.path}/{file.name}', callback=callback)
        return json_response(error=error)

    @auth('host.console.del')
    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='参数错误'),
            Argument('file', help='请输入文件路径')
        ).parse(request.GET)
        if error is None:
            if not has_host_perm(request.user, form.id):
                return json_response(error='无权访问主机，请联系管理员')
            host = Host.objects.get(pk=form.id)
            if not host:
                return json_response(error='未找到指定主机')
            with host.get_ssh() as ssh:
                ssh.remove_file(form.file)
        return json_response(error=error)

    def _compute_progress(self, rds_cli, token, total, value, *args):
        percent = '%.1f' % (value / total * 100)
        rds_cli.publish(token, percent)


class FileEditView(View):
    """SSH主机文件编辑功能"""
    @auth('host.console.list')
    def get(self, request):
        """读取文件内容"""
        form, error = JsonParser(
            Argument('id', type=int, help='参数错误'),
            Argument('file', help='请输入文件路径')
        ).parse(request.GET)
        if error:
            return json_response(error=error)
        
        if not has_host_perm(request.user, form.id):
            return json_response(error='无权访问主机，请联系管理员')
        
        host = Host.objects.filter(pk=form.id).first()
        if not host:
            return json_response(error='未找到指定主机')
        
        try:
            with host.get_ssh() as ssh:
                # 检查文件是否存在且可读
                exit_code, output = ssh.exec_command_raw(f'test -f "{form.file}" && test -r "{form.file}"')
                if exit_code != 0:
                    return json_response(error='文件不存在或无读取权限')
                
                # 文件大小检查已移除，允许编辑任意大小的文件
                # 注意：编辑超大文件可能会影响性能
                
                # 读取文件内容
                ssh_cli = ssh.get_client()
                sftp = ssh_cli.open_sftp()
                try:
                    with sftp.open(form.file, 'r') as f:
                        content = f.read()
                        # 尝试解码为UTF-8，失败则用latin-1
                        try:
                            if isinstance(content, bytes):
                                content = content.decode('utf-8')
                        except UnicodeDecodeError:
                            try:
                                content = content.decode('gbk')
                            except UnicodeDecodeError:
                                content = content.decode('latin-1')
                        
                        return json_response({
                            'content': content,
                            'file': form.file,
                            'size': len(content)
                        })
                finally:
                    sftp.close()
                    
        except Exception as e:
            return json_response(error=f'读取文件失败: {str(e)}')
    
    @auth('host.console.upload')
    def post(self, request):
        """保存文件内容"""
        form, error = JsonParser(
            Argument('id', type=int, help='参数错误'),
            Argument('file', help='请输入文件路径'),
            Argument('content', help='请输入文件内容')
        ).parse(request.body)
        if error:
            return json_response(error=error)
        
        if not has_host_perm(request.user, form.id):
            return json_response(error='无权访问主机，请联系管理员')
        
        host = Host.objects.filter(pk=form.id).first()
        if not host:
            return json_response(error='未找到指定主机')
        
        try:
            with host.get_ssh() as ssh:
                # 检查文件是否存在且可写
                exit_code, output = ssh.exec_command_raw(f'test -f "{form.file}" && test -w "{form.file}"')
                if exit_code != 0:
                    return json_response(error='文件不存在或无写入权限')
                
                # 备份原文件
                backup_file = f"{form.file}.spug_backup_{int(time.time())}"
                ssh.exec_command_raw(f'cp "{form.file}" "{backup_file}"')
                
                # 保存新内容
                ssh_cli = ssh.get_client()
                sftp = ssh_cli.open_sftp()
                try:
                    with sftp.open(form.file, 'w') as f:
                        if isinstance(form.content, str):
                            f.write(form.content.encode('utf-8'))
                        else:
                            f.write(form.content)
                    
                    # 删除备份文件（保存成功后）
                    ssh.exec_command_raw(f'rm -f "{backup_file}"')
                    
                    return json_response({'message': '文件保存成功'})
                except Exception as e:
                    # 恢复备份文件
                    ssh.exec_command_raw(f'mv "{backup_file}" "{form.file}"')
                    raise e
                finally:
                    sftp.close()
                    
        except Exception as e:
            return json_response(error=f'保存文件失败: {str(e)}')


class ScriptDistributeView(View):
    """脚本分发"""
    
    @auth('exec.transfer.do')
    def post(self, request):
        """分发脚本到目标主机"""
        form, error = JsonParser(
            Argument('script_path', help='请指定脚本文件路径'),
            Argument('script_name', help='请指定脚本文件名'),
            Argument('host_ids', type=list, filter=lambda x: len(x), help='请选择目标主机'),
            Argument('target_dir', default='/tmp', help='目标目录')
        ).parse(request.body)
        
        if error:
            return json_response(error=error)
        
        # 权限检查
        if not has_host_perm(request.user, form.host_ids):
            return json_response(error='无权访问主机，请联系管理员')
        
        # 检查脚本文件是否存在
        files_dir = os.path.join(settings.BASE_DIR, 'files')
        # 支持子目录结构的文件路径
        script_full_path = os.path.join(files_dir, form.script_path.lstrip('/'))
        
        if not os.path.exists(script_full_path):
            return json_response(error=f'脚本文件不存在: {form.script_path}')
        
        # 安全检查
        if not script_full_path.startswith(files_dir):
            return json_response(error='非法的文件路径')
        
        # 开始分发
        token = uuid.uuid4().hex
        Thread(target=self._distribute_script, args=(
            token, script_full_path, form.script_name, form.host_ids, form.target_dir
        )).start()
        
        return json_response({
            'success': True,
            'message': '脚本分发已开始',
            'token': token
        })
    
    def _distribute_script(self, token, script_path, script_name, host_ids, target_dir):
        """执行脚本分发任务"""
        rds = get_redis_connection()
        
        # 发布开始消息
        rds.publish(token, json.dumps({
            'type': 'info',
            'message': f'开始分发脚本: {script_name}'
        }))
        
        success_count = 0
        total_count = len(host_ids)
        
        for host_id in host_ids:
            host_name = "未知主机"
            try:
                host = Host.objects.get(pk=host_id)
                host_name = host.name
                
                rds.publish(token, json.dumps({
                    'type': 'info',
                    'message': f'正在连接主机: {host.name} ({host.hostname})'
                }))
                
                with host.get_ssh() as ssh:
                    # 确保目标目录存在
                    ssh.exec_command_raw(f'mkdir -p {target_dir}')
                    
                    # 上传脚本文件
                    remote_path = f"{target_dir}/{script_name}"
                    ssh.put_file(script_path, remote_path)
                    
                    # 赋予执行权限
                    ssh.exec_command_raw(f'chmod +x {remote_path}')
                    
                    success_count += 1
                    
                    rds.publish(token, json.dumps({
                        'type': 'success',
                        'message': f'✅ {host.name}: 分发成功'
                    }))
                    
            except Exception as e:
                rds.publish(token, json.dumps({
                    'type': 'error',
                    'message': f'❌ {host_name}: 分发失败 - {str(e)}'
                }))
        
        # 发布完成消息
        rds.publish(token, json.dumps({
            'type': 'complete',
            'message': f'脚本分发完成: 成功 {success_count}/{total_count}'
        }))


class FileManagerEditView(View):
    """本地文件编辑功能"""
    
    @auth('file.file.view')
    def get(self, request):
        """读取本地文件内容"""
        form, error = JsonParser(
            Argument('filename', help='请输入文件名')
        ).parse(request.GET)
        if error:
            return json_response(error=error)
        
        files_dir = os.path.join(settings.BASE_DIR, 'files')
        file_path = os.path.join(files_dir, form.filename)
        
        # 安全检查
        if not file_path.startswith(files_dir):
            return json_response(error='非法的文件路径')
        
        if not os.path.exists(file_path):
            return json_response(error='文件不存在')
        
        try:
            # 文件大小检查已移除，允许编辑任意大小的文件
            # 注意：编辑超大文件可能会影响性能
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            return json_response({
                'content': content,
                'filename': form.filename,
                'size': len(content)
            })
            
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
                return json_response({
                    'content': content,
                    'filename': form.filename,
                    'size': len(content)
                })
            except UnicodeDecodeError:
                return json_response(error='文件编码不支持，无法编辑')
        except Exception as e:
            return json_response(error=f'读取文件失败: {str(e)}')
    
    @auth('file.file.add')
    def post(self, request):
        """保存本地文件内容"""
        form, error = JsonParser(
            Argument('filename', help='请输入文件名'),
            Argument('content', help='请输入文件内容')
        ).parse(request.body)
        if error:
            return json_response(error=error)
        
        files_dir = os.path.join(settings.BASE_DIR, 'files')
        file_path = os.path.join(files_dir, form.filename)
        
        # 安全检查
        if not file_path.startswith(files_dir):
            return json_response(error='非法的文件路径')
        
        if not os.path.exists(file_path):
            return json_response(error='文件不存在')
        
        try:
            # 备份原文件
            backup_path = f"{file_path}.spug_backup_{int(time.time())}"
            shutil.copy2(file_path, backup_path)
            
            # 保存新内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(form.content)
            
            # 删除备份文件（保存成功后）
            os.remove(backup_path)
            
            return json_response({'message': '文件保存成功'})
            
        except Exception as e:
            # 恢复备份文件
            if os.path.exists(backup_path):
                shutil.move(backup_path, file_path)
            return json_response(error=f'保存文件失败: {str(e)}')


class FileManagerView(View):
    """文件管理器（扩展支持文件夹）"""
    
    @auth('file.file.view')
    def get(self, request):
        """获取文件列表 - 支持指定目录"""
        path = request.GET.get('path', '')
        files_dir = os.path.join(settings.BASE_DIR, 'files')
        
        # 处理相对路径
        if path:
            current_dir = os.path.join(files_dir, path.lstrip('/'))
        else:
            current_dir = files_dir
            
        # 安全检查
        if not current_dir.startswith(files_dir):
            return json_response(error='非法的路径')
            
        if not os.path.exists(current_dir):
            os.makedirs(current_dir, exist_ok=True)
        
        files = []
        folders = []
        
        try:
            for item in os.listdir(current_dir):
                item_path = os.path.join(current_dir, item)
                stat = os.stat(item_path)
                
                if os.path.isfile(item_path):
                    file_type = self._get_file_type(item)
                    files.append({
                        'name': item,
                        'size': stat.st_size,
                        'size_human': self._format_size(stat.st_size),
                        'modified': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat.st_mtime)),
                        'type': file_type,
                        'is_file': True
                    })
                elif os.path.isdir(item_path):
                    # 统计文件夹内容数量
                    try:
                        item_count = len(os.listdir(item_path))
                    except:
                        item_count = 0
                        
                    folders.append({
                        'name': item,
                        'size': 0,
                        'size_human': f'{item_count} 项',
                        'modified': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat.st_mtime)),
                        'type': 'folder',
                        'is_file': False,
                        'item_count': item_count
                    })
        
            # 文件夹排在前面，然后是文件
            all_items = sorted(folders, key=lambda x: x['name']) + sorted(files, key=lambda x: x['name'])
            
            return json_response({
                'files': all_items,
                'current_path': path,
                'parent_path': os.path.dirname(path) if path else None
            })
            
        except Exception as e:
            return json_response(error=f'读取目录失败: {str(e)}')
    
    @auth('file.file.add') 
    def post(self, request):
        """上传文件"""
        if 'file' not in request.FILES:
            return json_response(error='请选择要上传的文件')
        
        file = request.FILES['file']
        filename = file.name
        path = request.POST.get('path', '')  # 支持指定上传目录
        
        files_dir = os.path.join(settings.BASE_DIR, 'files')
        target_dir = os.path.join(files_dir, path.lstrip('/')) if path else files_dir
        
        # 安全检查
        if not target_dir.startswith(files_dir):
            return json_response(error='非法的路径')
        
        if not os.path.exists(target_dir):
            os.makedirs(target_dir, exist_ok=True)
        
        file_path = os.path.join(target_dir, filename)
        
        # 如果文件已存在，添加时间戳
        if os.path.exists(file_path):
            name, ext = os.path.splitext(filename)
            timestamp = int(time.time())
            filename = f"{name}_{timestamp}{ext}"
            file_path = os.path.join(target_dir, filename)
        
        with open(file_path, 'wb') as f:
            for chunk in file.chunks():
                f.write(chunk)
        
        return json_response({'message': '文件上传成功', 'filename': filename})
    
    @auth('file.file.del')
    def delete(self, request):
        """删除文件"""
        form, error = JsonParser(
            Argument('filename', help='请指定要删除的文件')
        ).parse(request.GET)
        
        if error:
            return json_response(error=error)
        
        files_dir = os.path.join(settings.BASE_DIR, 'files')
        file_path = os.path.join(files_dir, form.filename)
        
        if not os.path.exists(file_path):
            return json_response(error='文件不存在')
        
        # 安全检查
        if not file_path.startswith(files_dir):
            return json_response(error='非法的文件路径')
        
        os.remove(file_path)
        return json_response({'message': '文件删除成功'})
    
    def _get_file_type(self, filename):
        """判断文件类型"""
        ext = os.path.splitext(filename)[1].lower()
        
        script_exts = ['.sh', '.py', '.pl', '.rb', '.js']
        code_exts = ['.html', '.css', '.js', '.json', '.xml', '.yml', '.yaml']
        text_exts = ['.txt', '.md', '.log', '.conf', '.cfg']
        image_exts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico']
        archive_exts = ['.zip', '.tar', '.gz', '.bz2', '.7z', '.rar']
        doc_exts = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']
        
        if ext in script_exts:
            return 'script'
        elif ext in code_exts:
            return 'code'
        elif ext in text_exts:
            return 'text'
        elif ext in image_exts:
            return 'image'
        elif ext in archive_exts:
            return 'archive'
        elif ext in doc_exts:
            return 'document'
        else:
            return 'unknown'
    
    def _format_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"


class FileDownloadView(View):
    """文件下载"""
    
    @auth('file.file.view')
    def get(self, request, filename):
        """下载文件"""
        files_dir = os.path.join(settings.BASE_DIR, 'files')
        file_path = os.path.join(files_dir, filename)
        
        if not os.path.exists(file_path):
            raise Http404('文件不存在')
        
        # 安全检查
        if not file_path.startswith(files_dir):
            raise Http404('非法的文件路径')
        
        # 设置响应头
        response = HttpResponse()
        response['Content-Type'] = mimetypes.guess_type(filename)[0] or 'application/octet-stream'
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        response['Content-Length'] = os.path.getsize(file_path)
        
        # 读取文件内容
        with open(file_path, 'rb') as f:
            response.write(f.read())
        
        return response


class FolderManagerView(View):
    """文件夹管理器"""
    
    @auth('file.file.add')
    def post(self, request):
        """创建文件夹"""
        form, error = JsonParser(
            Argument('path', default='', required=False, help='父目录路径'),
            Argument('name', help='请输入文件夹名称')
        ).parse(request.body)
        
        if error:
            return json_response(error=error)
        
        # 验证文件夹名称
        if not form.name or '/' in form.name or '\\' in form.name:
            return json_response(error='文件夹名称不能包含 / 或 \\')
        
        files_dir = os.path.join(settings.BASE_DIR, 'files')
        # 处理path参数，确保为字符串类型
        path = form.path or ''
        parent_path = os.path.join(files_dir, path.lstrip('/')) if path else files_dir
        folder_path = os.path.join(parent_path, form.name)
        
        # 安全检查
        if not folder_path.startswith(files_dir):
            return json_response(error='非法的文件夹路径')
        
        if os.path.exists(folder_path):
            return json_response(error='文件夹已存在')
        
        try:
            os.makedirs(folder_path)
            return json_response({'message': '文件夹创建成功'})
        except Exception as e:
            return json_response(error=f'创建文件夹失败: {str(e)}')
    
    @auth('file.file.del')
    def delete(self, request):
        """删除文件夹"""
        form, error = JsonParser(
            Argument('path', help='请指定要删除的文件夹路径')
        ).parse(request.GET)
        
        if error:
            return json_response(error=error)
        
        files_dir = os.path.join(settings.BASE_DIR, 'files')
        folder_path = os.path.join(files_dir, form.path.lstrip('/'))
        
        # 安全检查
        if not folder_path.startswith(files_dir):
            return json_response(error='非法的文件夹路径')
        
        if not os.path.exists(folder_path):
            return json_response(error='文件夹不存在')
        
        if not os.path.isdir(folder_path):
            return json_response(error='指定路径不是文件夹')
        
        # 检查是否为空文件夹
        if os.listdir(folder_path):
            return json_response(error='文件夹不为空，无法删除')
        
        try:
            os.rmdir(folder_path)
            return json_response({'message': '文件夹删除成功'})
        except Exception as e:
            return json_response(error=f'删除文件夹失败: {str(e)}')
    
    @auth('file.file.edit')
    def put(self, request):
        """移动文件或文件夹"""
        form, error = JsonParser(
            Argument('source_path', help='请指定源路径'),
            Argument('target_path', default='', required=False, help='目标路径'),
            Argument('name', help='请指定文件或文件夹名称')
        ).parse(request.body)
        
        if error:
            return json_response(error=error)
        
        files_dir = os.path.join(settings.BASE_DIR, 'files')
        
        # 构建源文件完整路径
        source_full_path = os.path.join(files_dir, form.source_path.lstrip('/'))
        
        # 构建目标目录路径
        target_dir = os.path.join(files_dir, form.target_path.lstrip('/')) if form.target_path else files_dir
        target_full_path = os.path.join(target_dir, form.name)
        
        # 安全检查
        if not source_full_path.startswith(files_dir) or not target_full_path.startswith(files_dir):
            return json_response(error='非法的文件路径')
        
        if not os.path.exists(source_full_path):
            return json_response(error='源文件或文件夹不存在')
        
        if os.path.exists(target_full_path):
            return json_response(error='目标位置已存在同名文件或文件夹')
        
        # 确保目标目录存在
        if not os.path.exists(target_dir):
            try:
                os.makedirs(target_dir, exist_ok=True)
            except Exception as e:
                return json_response(error=f'创建目标目录失败: {str(e)}')
        
        try:
            shutil.move(source_full_path, target_full_path)
            return json_response({'message': '移动成功'})
        except Exception as e:
            return json_response(error=f'移动失败: {str(e)}')


class FileTreeView(View):
    """文件树API"""
    
    @auth('file.file.view')
    def get(self, request):
        """获取文件树结构"""
        files_dir = os.path.join(settings.BASE_DIR, 'files')
        if not os.path.exists(files_dir):
            os.makedirs(files_dir)
        
        def build_tree(path, relative_path=''):
            """递归构建文件树"""
            tree = []
            try:
                for item in sorted(os.listdir(path)):
                    item_path = os.path.join(path, item)
                    item_relative_path = os.path.join(relative_path, item) if relative_path else item
                    
                    if os.path.isdir(item_path):
                        # 统计文件夹内容
                        try:
                            item_count = len([f for f in os.listdir(item_path) 
                                            if os.path.isfile(os.path.join(item_path, f))])
                            folder_count = len([f for f in os.listdir(item_path) 
                                              if os.path.isdir(os.path.join(item_path, f))])
                        except:
                            item_count = 0
                            folder_count = 0
                        
                        node = {
                            'key': item_relative_path.replace('\\', '/'),
                            'title': item,
                            'type': 'folder',
                            'isLeaf': False,
                            'children': build_tree(item_path, item_relative_path),
                            'item_count': item_count,
                            'folder_count': folder_count
                        }
                        tree.append(node)
                    else:
                        # 文件节点
                        stat = os.stat(item_path)
                        file_type = self._get_file_type(item)
                        node = {
                            'key': item_relative_path.replace('\\', '/'),
                            'title': item,
                            'type': file_type,
                            'isLeaf': True,
                            'size': stat.st_size,
                            'size_human': self._format_size(stat.st_size)
                        }
                        tree.append(node)
            except Exception as e:
                print(f"Error building tree for {path}: {e}")
            
            return tree
        
        tree = build_tree(files_dir)
        
        # 添加远程文件夹
        try:
            from apps.file.models import RemoteFolder
            remote_folders = RemoteFolder.objects.filter(is_active=True).order_by('-created_at')
            
            if remote_folders:
                # 创建远程文件夹根节点
                remote_root = {
                    'key': '_remote_folders_',
                    'title': '远程文件夹',
                    'type': 'remote_root',
                    'isLeaf': False,
                    'children': [],
                    'icon': 'cloud'
                }
                
                for folder in remote_folders:
                    remote_node = {
                        'key': f'remote_{folder.id}',
                        'title': folder.name,
                        'type': 'remote_folder',
                        'isLeaf': False,
                        'remote_path': folder.remote_path,
                        'description': folder.description,
                        'folder_id': folder.id,
                        'icon': 'cloud',
                        'children': []  # 远程文件夹的子内容在需要时动态加载
                    }
                    remote_root['children'].append(remote_node)
                
                # 将远程文件夹根节点添加到树的开头
                tree.insert(0, remote_root)
                
        except Exception as e:
            print(f"Error loading remote folders: {e}")
        
        return json_response({'tree': tree})
    
    def _get_file_type(self, filename):
        """判断文件类型"""
        ext = os.path.splitext(filename)[1].lower()
        
        script_exts = ['.sh', '.py', '.pl', '.rb', '.js']
        code_exts = ['.html', '.css', '.js', '.json', '.xml', '.yml', '.yaml', '.ts', '.go', '.java', '.c', '.cpp']
        text_exts = ['.txt', '.md', '.log', '.conf', '.cfg', '.ini', '.properties']
        image_exts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico']
        archive_exts = ['.zip', '.tar', '.gz', '.bz2', '.7z', '.rar']
        doc_exts = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']
        
        if ext in script_exts:
            return 'script'
        elif ext in code_exts:
            return 'code'
        elif ext in text_exts:
            return 'text'
        elif ext in image_exts:
            return 'image'
        elif ext in archive_exts:
            return 'archive'
        elif ext in doc_exts:
            return 'document'
        else:
            return 'unknown'
    
    def _format_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"


class RemoteFolderView(View):
    """远程文件夹配置管理"""
    
    @auth('file.file.view')
    def get(self, request):
        """获取远程文件夹配置列表"""
        from apps.file.models import RemoteFolder
        
        folders = RemoteFolder.objects.filter(is_active=True).order_by('-created_at')
        return json_response([folder.to_dict() for folder in folders])
    
    @auth('file.file.add')
    def post(self, request):
        """创建远程文件夹配置"""
        from apps.file.models import RemoteFolder
        
        form, error = JsonParser(
            Argument('name', help='请输入配置名称'),
            Argument('remote_path', help='请输入远程路径'),
            Argument('username', required=False),
            Argument('password', required=False),
            Argument('domain', required=False),
            Argument('description', required=False)
        ).parse(request.body)
        
        if error:
            return json_response(error=error)
        
        # 验证远程路径格式
        if not form.remote_path.startswith('\\\\'):
            return json_response(error='远程路径格式错误，应以 \\\\ 开头')
        
        # 检查配置名称是否重复
        if RemoteFolder.objects.filter(name=form.name, is_active=True).exists():
            return json_response(error='配置名称已存在')
        
        try:
            # 测试连接
            from apps.file.remote_manager import RemoteFileManager
            
            remote_manager = RemoteFileManager(
                form.remote_path,
                form.username,
                form.password,
                form.domain
            )
            
            if not remote_manager.connect():
                return json_response(error='无法连接到远程路径，请检查路径和凭据')
            
            remote_manager.disconnect()
            
            # 创建配置
            folder = RemoteFolder.objects.create(
                name=form.name,
                remote_path=form.remote_path,
                username=form.username,
                password=form.password,
                domain=form.domain,
                description=form.description
            )
            
            return json_response({'message': '远程文件夹配置创建成功', 'id': folder.id})
            
        except Exception as e:
            return json_response(error=f'创建失败: {str(e)}')
    
    @auth('file.file.edit')
    def put(self, request):
        """更新远程文件夹配置"""
        from apps.file.models import RemoteFolder
        
        form, error = JsonParser(
            Argument('id', type=int, help='请指定配置ID'),
            Argument('name', help='请输入配置名称'),
            Argument('remote_path', help='请输入远程路径'),
            Argument('username', required=False),
            Argument('password', required=False),
            Argument('domain', required=False),
            Argument('description', required=False)
        ).parse(request.body)
        
        if error:
            return json_response(error=error)
        
        try:
            folder = RemoteFolder.objects.get(id=form.id, is_active=True)
            
            # 检查配置名称是否重复（排除自己）
            if RemoteFolder.objects.filter(name=form.name, is_active=True).exclude(id=form.id).exists():
                return json_response(error='配置名称已存在')
            
            # 测试连接
            from apps.file.remote_manager import RemoteFileManager
            
            remote_manager = RemoteFileManager(
                form.remote_path,
                form.username,
                form.password,
                form.domain
            )
            
            if not remote_manager.connect():
                return json_response(error='无法连接到远程路径，请检查路径和凭据')
            
            remote_manager.disconnect()
            
            # 更新配置
            folder.name = form.name
            folder.remote_path = form.remote_path
            folder.username = form.username
            folder.password = form.password
            folder.domain = form.domain
            folder.description = form.description
            folder.save()
            
            return json_response({'message': '更新成功'})
            
        except RemoteFolder.DoesNotExist:
            return json_response(error='配置不存在')
        except Exception as e:
            return json_response(error=f'更新失败: {str(e)}')
    
    @auth('file.file.del')
    def delete(self, request):
        """删除远程文件夹配置"""
        from apps.file.models import RemoteFolder
        
        form, error = JsonParser(
            Argument('id', type=int, help='请指定配置ID')
        ).parse(request.GET)
        
        if error:
            return json_response(error=error)
        
        try:
            folder = RemoteFolder.objects.get(id=form.id, is_active=True)
            folder.is_active = False
            folder.save()
            
            return json_response({'message': '删除成功'})
            
        except RemoteFolder.DoesNotExist:
            return json_response(error='配置不存在')


class RemoteFileManagerView(View):
    """远程文件管理器"""
    
    @auth('file.file.view')
    def get(self, request):
        """获取远程文件列表"""
        from apps.file.models import RemoteFolder
        from apps.file.remote_manager import RemoteFileManager
        from apps.file.redis_cache_service import cache_service
        
        form, error = JsonParser(
            Argument('folder_id', type=int, help='请指定远程文件夹配置ID'),
            Argument('path', default='', required=False),
            Argument('force_refresh', type=bool, default=False, required=False)
        ).parse(request.GET)
        
        if error:
            return json_response(error=error)
        
        try:
            folder_config = RemoteFolder.objects.get(id=form.folder_id, is_active=True)
            
            # 优先使用缓存，除非强制刷新
            if not form.force_refresh:
                files, is_from_cache = cache_service.get_cached_files(form.folder_id, form.path)
                
                return json_response({
                    'files': files,
                    'current_path': form.path,
                    'folder_name': folder_config.name,
                    'remote_path': folder_config.remote_path,
                    'from_cache': is_from_cache,
                    'cache_status': '使用缓存数据' if is_from_cache else '实时获取数据'
                })
            
            # 强制刷新，实时获取
            remote_manager = RemoteFileManager(
                folder_config.remote_path,
                folder_config.username,
                folder_config.password,
                folder_config.domain
            )
            
            if not remote_manager.connect():
                return json_response(error='无法连接到远程路径')
            
            try:
                files = remote_manager.list_files(form.path)
                
                # 更新缓存
                cache_service._update_cache_success(folder_config, form.path, files)
                
                return json_response({
                    'files': files,
                    'current_path': form.path,
                    'folder_name': folder_config.name,
                    'remote_path': folder_config.remote_path,
                    'from_cache': False,
                    'cache_status': '强制刷新数据'
                })
                
            finally:
                remote_manager.disconnect()
                
        except RemoteFolder.DoesNotExist:
            return json_response(error='远程文件夹配置不存在')
        except Exception as e:
            return json_response(error=f'获取文件列表失败: {str(e)}')
    
    @auth('file.file.view')
    def post(self, request):
        """从远程文件夹下载文件到本地"""
        from apps.file.models import RemoteFolder
        from apps.file.remote_manager import RemoteFileManager
        
        form, error = JsonParser(
            Argument('folder_id', type=int, help='请指定远程文件夹配置ID'),
            Argument('remote_file_path', help='请指定远程文件路径'),
            Argument('local_path', default='', required=False)
        ).parse(request.body)
        
        if error:
            return json_response(error=error)
        
        try:
            folder_config = RemoteFolder.objects.get(id=form.folder_id, is_active=True)
            
            remote_manager = RemoteFileManager(
                folder_config.remote_path,
                folder_config.username,
                folder_config.password,
                folder_config.domain
            )
            
            if not remote_manager.connect():
                return json_response(error='无法连接到远程路径')
            
            try:
                # 确定本地保存路径
                files_dir = os.path.join(settings.BASE_DIR, 'files')
                if form.local_path:
                    local_dir = os.path.join(files_dir, form.local_path.lstrip('/'))
                else:
                    local_dir = files_dir
                
                if not os.path.exists(local_dir):
                    os.makedirs(local_dir, exist_ok=True)
                
                filename = os.path.basename(form.remote_file_path)
                local_file_path = os.path.join(local_dir, filename)
                
                # 获取远程文件的完整路径
                remote_dir = os.path.dirname(form.remote_file_path)
                remote_base_path = folder_config.remote_path
                
                # 根据操作系统调整路径分隔符
                path_sep = '\\' if platform.system() == 'Windows' else '/'
                
                # 生成多种可能的路径格式，以便尝试
                remote_paths_to_try = []
                
                if platform.system() == 'Windows':
                    # Windows环境下
                    if remote_base_path.startswith('\\\\') or remote_base_path.startswith('//'):
                        # 网络共享路径
                        if remote_dir:
                            # 1. Windows格式 - 网络共享路径 + 子目录 + 文件名
                            path1 = os.path.join(remote_base_path, remote_dir, filename).replace('/', '\\')
                            remote_paths_to_try.append(path1)
                            
                            # 2. 尝试不同的路径分隔符
                            path2 = remote_base_path.replace('/', '\\') + '\\' + remote_dir.replace('/', '\\') + '\\' + filename
                            if path2 != path1:
                                remote_paths_to_try.append(path2)
                        else:
                            # 网络共享路径 + 文件名
                            path1 = os.path.join(remote_base_path, filename).replace('/', '\\')
                            remote_paths_to_try.append(path1)
                            
                            path2 = remote_base_path.replace('/', '\\') + '\\' + filename
                            if path2 != path1:
                                remote_paths_to_try.append(path2)
                    else:
                        # 本地路径或其他格式
                        if remote_dir:
                            path1 = os.path.join(remote_base_path, remote_dir, filename)
                            remote_paths_to_try.append(path1)
                        else:
                            path1 = os.path.join(remote_base_path, filename)
                            remote_paths_to_try.append(path1)
                else:
                    # Linux环境下
                    if remote_dir:
                        path1 = os.path.join(remote_base_path, remote_dir, filename).replace('\\', '/')
                        remote_paths_to_try.append(path1)
                    else:
                        path1 = os.path.join(remote_base_path, filename).replace('\\', '/')
                        remote_paths_to_try.append(path1)
                
                # 添加原始路径作为备选
                if form.remote_file_path not in remote_paths_to_try:
                    remote_paths_to_try.append(form.remote_file_path)
                
                logger.info(f"将尝试以下远程文件路径: {remote_paths_to_try}")
                logger.info(f"本地文件保存路径: {local_file_path}")
                
                # 下载远程文件到本地 - 尝试多种路径格式
                logger.info("开始下载文件...")
                download_success = False
                last_error = None
                
                for i, remote_path in enumerate(remote_paths_to_try):
                    try:
                        logger.info(f"尝试路径 {i+1}/{len(remote_paths_to_try)}: {remote_path}")
                        if remote_manager.download_file(remote_path, local_file_path):
                            download_success = True
                            logger.info(f"使用路径成功下载: {remote_path}")
                            break
                    except Exception as e:
                        last_error = str(e)
                        logger.warning(f"使用路径 {remote_path} 下载失败: {str(e)}")
                
                if not download_success:
                    error_msg = f"转移文件失败: 无法下载文件 {filename}"
                    if last_error:
                        error_msg += f" (错误: {last_error})"
                    logger.error(error_msg)
                    return json_response(error=error_msg)
                
                # 检查文件是否成功下载
                if not os.path.exists(local_file_path):
                    logger.error(f"下载后文件不存在: {local_file_path}")
                    return json_response(error=f'转移文件失败: 下载的文件不存在')
                
                if os.path.getsize(local_file_path) == 0:
                    logger.error(f"下载的文件为空: {local_file_path}")
                    return json_response(error=f'转移文件失败: 下载的文件为空')
                
                # 获取相对于files目录的路径，用于前端显示
                relative_path = os.path.relpath(local_file_path, files_dir)
                logger.info(f"文件转移成功: {relative_path}")
                
                return json_response({
                    'message': '文件转移成功',
                    'file_name': filename,
                    'local_path': relative_path.replace('\\', '/'),
                    'full_path': local_file_path
                })
                
            finally:
                remote_manager.disconnect()
                
        except RemoteFolder.DoesNotExist:
            logger.error(f"远程文件夹不存在: {form.folder_id}")
            return json_response(error=f'远程文件夹不存在: {form.folder_id}')
        except Exception as e:
            logger.error(f"转移文件失败: {str(e)}")
            return json_response(error=f'转移文件失败: {str(e)}')


class RemoteConnectView(View):
    """临时远程文件夹连接"""
    
    @auth('file.file.view')
    def post(self, request):
        """临时连接远程文件夹"""
        from apps.file.remote_manager import RemoteFileManager
        
        form, error = JsonParser(
            Argument('remote_path', help='请输入远程路径'),
            Argument('username', required=False),
            Argument('password', required=False),
            Argument('domain', required=False),
            Argument('save_as_persistent', type=bool, required=False),
            Argument('folder_name', required=False),
            Argument('description', required=False)
        ).parse(request.body)
        
        if error:
            return json_response(error=error)
        
        try:
            import logging
            logger = logging.getLogger(__name__)
            
            # 记录连接尝试
            logger.info(f"尝试连接远程路径: {form.remote_path}, 用户: {form.username}")
            
            # 创建远程文件管理器实例
            remote_manager = RemoteFileManager(
                form.remote_path,
                form.username,
                form.password,
                form.domain
            )
            
            # 测试连接
            connect_result = remote_manager.connect()
            logger.info(f"连接结果: {connect_result}")
            
            if not connect_result:
                # 尝试分析具体原因
                import subprocess
                import os
                
                error_details = []
                
                # 检查网络连通性
                try:
                    parts = form.remote_path[2:].split('\\', 2) if form.remote_path.startswith('\\\\') else []
                    if len(parts) >= 1:
                        server = parts[0]
                        result = subprocess.run(['ping', '-n', '1', server], 
                                              capture_output=True, text=True, shell=True)
                        if result.returncode != 0:
                            error_details.append(f"网络不通: 无法ping通 {server}")
                except:
                    pass
                
                # 检查是否存在已有连接
                try:
                    result = subprocess.run(['net', 'use'], capture_output=True, text=True, shell=True)
                    if result.returncode == 0:
                        network_path = f"\\\\{remote_manager.server}\\{remote_manager.share}"
                        if network_path in result.stdout:
                            # 已有连接，检查是否可以访问
                            if os.path.exists(network_path):
                                error_details.append("已有连接但认证用户不同")
                            else:
                                error_details.append("连接存在但无法访问")
                        else:
                            error_details.append("未找到已有连接")
                except:
                    pass
                
                message = "无法连接到远程路径，请检查路径和凭据"
                if error_details:
                    message += f" (详情: {'; '.join(error_details)})"
                
                return json_response({
                    'success': False,
                    'message': message
                })
            
            try:
                # 尝试列出根目录文件
                files = remote_manager.list_files('')
                logger.info(f"成功获取文件列表，文件数量: {len(files)}")
                
                # 如果用户选择保存为持久目录
                saved_folder = None
                if form.save_as_persistent and form.folder_name:
                    try:
                        from apps.file.models import RemoteFolder
                        
                        # 检查是否已存在相同名称的配置
                        existing = RemoteFolder.objects.filter(name=form.folder_name).first()
                        if existing:
                            return json_response({
                                'success': False,
                                'message': f'目录名称 "{form.folder_name}" 已存在，请选择其他名称'
                            })
                        
                        # 创建新的远程文件夹配置
                        saved_folder = RemoteFolder.objects.create(
                            name=form.folder_name,
                            remote_path=form.remote_path,
                            username=form.username,
                            password=form.password,
                            domain=form.domain,
                            description=form.description,
                            is_active=True
                        )
                        
                        logger.info(f"远程文件夹已保存: {saved_folder.name}")
                        
                    except Exception as e:
                        logger.error(f"保存远程文件夹失败: {e}")
                        return json_response({
                            'success': False,
                            'message': f'保存目录失败: {str(e)}'
                        })
                
                # 连接成功，先尝试保存凭据到session（如果session可用）
                try:
                    if hasattr(request, 'session'):
                        request.session['remote_credentials'] = {
                            'remote_path': form.remote_path,
                            'username': form.username,
                            'password': form.password,
                            'domain': form.domain,
                        }
                        logger.info("凭据已保存到session")
                    else:
                        logger.warning("Session不可用，跳过凭据保存")
                except Exception as e:
                    logger.warning(f"保存凭据到session失败: {e}")
                
                message = '远程文件夹连接成功'
                if saved_folder:
                    message += f'，已保存为目录 "{saved_folder.name}"'
                
                return json_response({
                    'success': True,
                    'message': message,
                    'files': files,
                    'saved_folder_id': saved_folder.id if saved_folder else None,
                    'credentials': {
                        'remote_path': form.remote_path,
                        'username': form.username,
                        'password': form.password,
                        'domain': form.domain,
                    }
                })
                
            finally:
                remote_manager.disconnect()
                
        except Exception as e:
            import traceback
            logger.error(f"连接异常: {str(e)}")
            logger.error(f"详细错误: {traceback.format_exc()}")
            
            return json_response({
                'success': False,
                'message': f'连接失败: {str(e)}'
            })


class RemoteTempFileManagerView(View):
    """临时远程文件管理器（基于session）"""
    
    @auth('file.file.view')
    def get(self, request):
        """获取远程文件列表（优先使用缓存，支持session凭据或请求参数）"""
        from apps.file.remote_manager import RemoteFileManager
        from apps.file.redis_cache_service import cache_service
        from apps.file.models import RemoteFolder
        
        # 优先从session获取凭据，如果没有则从请求参数获取
        credentials = None
        
        # 方法1：从session获取
        try:
            if hasattr(request, 'session'):
                credentials = request.session.get('remote_credentials')
        except Exception as e:
            print(f"获取session凭据失败: {e}")
        
        # 方法2：从请求参数获取
        if not credentials:
            remote_path = request.GET.get('remote_path')
            username = request.GET.get('username')
            password = request.GET.get('password')
            domain = request.GET.get('domain')
            
            if remote_path:
                credentials = {
                    'remote_path': remote_path,
                    'username': username,
                    'password': password,
                    'domain': domain
                }
        
        if not credentials:
            return json_response(error='未找到远程连接凭据，请重新连接')
        
        form, error = JsonParser(
            Argument('path', default='', required=False),
            Argument('force_refresh', type=bool, default=False, required=False)
        ).parse(request.GET)
        
        if error:
            return json_response(error=error)
        
        try:
            # 尝试找到对应的持久化远程文件夹配置来使用缓存
            remote_folder = RemoteFolder.objects.filter(
                remote_path=credentials['remote_path'],
                is_active=True
            ).first()
            
            if remote_folder and not form.force_refresh:
                # 使用缓存服务
                from apps.file.redis_cache_service import cache_service
                files, is_from_cache = cache_service.get_cached_files(remote_folder.id, form.path)
                
                return json_response({
                    'files': files,
                    'current_path': form.path,
                    'remote_path': credentials['remote_path'],
                    'from_cache': is_from_cache,
                    'cache_status': '使用缓存数据' if is_from_cache else '实时获取数据'
                })
            
            # fallback到实时获取（临时连接或强制刷新）
            remote_manager = RemoteFileManager(
                credentials['remote_path'],
                credentials['username'],
                credentials['password'],
                credentials['domain']
            )
            
            if not remote_manager.connect():
                return json_response(error='无法连接到远程路径')
            
            try:
                files = remote_manager.list_files(form.path)
                
                return json_response({
                    'files': files,
                    'current_path': form.path,
                    'remote_path': credentials['remote_path'],
                    'from_cache': False,
                    'cache_status': '实时获取数据'
                })
                
            finally:
                remote_manager.disconnect()
                
        except Exception as e:
            return json_response(error=f'获取文件列表失败: {str(e)}')
    
    @auth('file.file.view')
    def post(self, request):
        """从远程文件夹下载文件到本地（基于session凭据或请求参数）"""
        from apps.file.remote_manager import RemoteFileManager
        
        # 优先从session获取凭据，如果没有则从请求body获取
        credentials = None
        
        # 方法1：从session获取
        try:
            if hasattr(request, 'session'):
                credentials = request.session.get('remote_credentials')
        except Exception as e:
            print(f"获取session凭据失败: {e}")
        
        # 方法2：从请求body获取
        if not credentials:
            form_data = JsonParser().parse(request.body)[0]
            if form_data and form_data.get('remote_path'):
                credentials = {
                    'remote_path': form_data.get('remote_path'),
                    'username': form_data.get('username'),
                    'password': form_data.get('password'),
                    'domain': form_data.get('domain')
                }
        
        if not credentials:
            return json_response(error='未找到远程连接凭据，请重新连接')
        
        form, error = JsonParser(
            Argument('remote_file_path', help='请指定远程文件路径'),
            Argument('local_path', default='', required=False)
        ).parse(request.body)
        
        if error:
            return json_response(error=error)
        
        try:
            remote_manager = RemoteFileManager(
                credentials['remote_path'],
                credentials['username'],
                credentials['password'],
                credentials['domain']
            )
            
            if not remote_manager.connect():
                return json_response(error='无法连接到远程路径')
            
            try:
                # 确定本地保存路径
                files_dir = os.path.join(settings.BASE_DIR, 'files')
                if form.local_path:
                    local_dir = os.path.join(files_dir, form.local_path.lstrip('/'))
                else:
                    local_dir = files_dir
                
                if not os.path.exists(local_dir):
                    os.makedirs(local_dir, exist_ok=True)
                
                filename = os.path.basename(form.remote_file_path)
                local_file_path = os.path.join(local_dir, filename)
                
                # 获取远程文件的完整路径
                remote_dir = os.path.dirname(form.remote_file_path)
                remote_base_path = credentials['remote_path']
                
                # 根据操作系统调整路径分隔符
                path_sep = '\\' if platform.system() == 'Windows' else '/'
                
                # 生成多种可能的路径格式，以便尝试
                remote_paths_to_try = []
                
                if platform.system() == 'Windows':
                    # Windows环境下
                    if remote_base_path.startswith('\\\\') or remote_base_path.startswith('//'):
                        # 网络共享路径
                        if remote_dir:
                            # 1. Windows格式 - 网络共享路径 + 子目录 + 文件名
                            path1 = os.path.join(remote_base_path, remote_dir, filename).replace('/', '\\')
                            remote_paths_to_try.append(path1)
                            
                            # 2. 尝试不同的路径分隔符
                            path2 = remote_base_path.replace('/', '\\') + '\\' + remote_dir.replace('/', '\\') + '\\' + filename
                            if path2 != path1:
                                remote_paths_to_try.append(path2)
                        else:
                            # 网络共享路径 + 文件名
                            path1 = os.path.join(remote_base_path, filename).replace('/', '\\')
                            remote_paths_to_try.append(path1)
                            
                            path2 = remote_base_path.replace('/', '\\') + '\\' + filename
                            if path2 != path1:
                                remote_paths_to_try.append(path2)
                    else:
                        # 本地路径或其他格式
                        if remote_dir:
                            path1 = os.path.join(remote_base_path, remote_dir, filename)
                            remote_paths_to_try.append(path1)
                        else:
                            path1 = os.path.join(remote_base_path, filename)
                            remote_paths_to_try.append(path1)
                else:
                    # Linux环境下
                    if remote_dir:
                        path1 = os.path.join(remote_base_path, remote_dir, filename).replace('\\', '/')
                        remote_paths_to_try.append(path1)
                    else:
                        path1 = os.path.join(remote_base_path, filename).replace('\\', '/')
                        remote_paths_to_try.append(path1)
                
                # 添加原始路径作为备选
                if form.remote_file_path not in remote_paths_to_try:
                    remote_paths_to_try.append(form.remote_file_path)
                
                logger.info(f"将尝试以下远程文件路径: {remote_paths_to_try}")
                logger.info(f"本地文件保存路径: {local_file_path}")
                
                # 下载远程文件到本地 - 尝试多种路径格式
                logger.info("开始下载文件...")
                download_success = False
                last_error = None
                
                for i, remote_path in enumerate(remote_paths_to_try):
                    try:
                        logger.info(f"尝试路径 {i+1}/{len(remote_paths_to_try)}: {remote_path}")
                        if remote_manager.download_file(remote_path, local_file_path):
                            download_success = True
                            logger.info(f"使用路径成功下载: {remote_path}")
                            break
                    except Exception as e:
                        last_error = str(e)
                        logger.warning(f"使用路径 {remote_path} 下载失败: {str(e)}")
                
                if not download_success:
                    error_msg = f"转移文件失败: 无法下载文件 {filename}"
                    if last_error:
                        error_msg += f" (错误: {last_error})"
                    logger.error(error_msg)
                    return json_response(error=error_msg)
                
                # 检查文件是否成功下载
                if not os.path.exists(local_file_path):
                    logger.error(f"下载后文件不存在: {local_file_path}")
                    return json_response(error=f'转移文件失败: 下载的文件不存在')
                
                if os.path.getsize(local_file_path) == 0:
                    logger.error(f"下载的文件为空: {local_file_path}")
                    return json_response(error=f'转移文件失败: 下载的文件为空')
                
                # 获取相对于files目录的路径，用于前端显示
                relative_path = os.path.relpath(local_file_path, files_dir)
                logger.info(f"文件转移成功: {relative_path}")
                
                return json_response({
                    'message': '文件转移成功',
                    'file_name': filename,
                    'local_path': relative_path.replace('\\', '/'),
                    'full_path': local_file_path
                })
                
            finally:
                remote_manager.disconnect()
                
        except RemoteFolder.DoesNotExist:
            logger.error(f"远程文件夹不存在: {form.folder_id}")
            return json_response(error=f'远程文件夹不存在: {form.folder_id}')
        except Exception as e:
            logger.error(f"转移文件失败: {str(e)}")
            return json_response(error=f'转移文件失败: {str(e)}')


class RemoteCacheManagementView(View):
    """远程文件夹缓存管理"""
    
    @auth('file.file.view')
    def get(self, request):
        """获取缓存状态"""
        from apps.file.redis_cache_service import cache_service
        
        action = request.GET.get('action', 'status')
        
        if action == 'status':
            status = cache_service.get_cache_status()
            return json_response(status)
        else:
            return json_response(error='不支持的操作')
    
    @auth('file.file.edit')
    def post(self, request):
        """缓存操作"""
        from apps.file.redis_cache_service import cache_service
        
        form, error = JsonParser(
            Argument('action', help='操作类型：refresh_all, refresh_folder, clear_expired, cache_file_tree'),
            Argument('folder_id', type=int, required=False),
            Argument('path', default='', required=False),
            Argument('recursive', type=bool, default=False, required=False)
        ).parse(request.body)
        
        if error:
            return json_response(error=error)
        
        try:
            if form.action == 'refresh_all':
                # 刷新所有缓存
                stats = cache_service.refresh_all_caches()
                return json_response({
                    'message': '缓存刷新完成',
                    'stats': stats
                })
            
            elif form.action == 'refresh_folder':
                if not form.folder_id:
                    return json_response(error='请指定文件夹ID')
                
                # 刷新指定文件夹缓存
                success = cache_service.refresh_folder_cache(form.folder_id, form.path, form.recursive)
                if success:
                    return json_response({'message': '文件夹缓存刷新成功'})
                else:
                    return json_response(error='文件夹缓存刷新失败')
            
            elif form.action == 'cache_file_tree':
                if not form.folder_id:
                    return json_response(error='请指定文件夹ID')
                
                # 递归缓存整个文件树
                stats = cache_service.cache_entire_file_tree(form.folder_id)
                
                if 'error' not in stats:
                    return json_response({
                        'message': f'文件树缓存成功，共缓存了 {stats.get("success_paths", 0)} 个路径',
                        'stats': stats
                    })
                else:
                    return json_response(error=f'文件树缓存失败: {stats.get("error")}')
            
            elif form.action == 'clear_expired':
                # 清理过期缓存
                count = cache_service.clear_expired_caches()
                return json_response({
                    'message': f'清理完成，删除了 {count} 条过期缓存'
                })
            
            else:
                return json_response(error='不支持的操作类型')
                
        except Exception as e:
            return json_response(error=f'操作失败: {str(e)}')


@method_route(methods=['POST'])
def connect(request):
    form, error = JsonParser(
        Argument('remote_path', help='请输入远程路径'),
        Argument('username', help='请输入用户名'),
        Argument('password', help='请输入密码'),
        Argument('domain', required=False),
        Argument('auto_cache', type=bool, default=False)
    ).parse(request.body)
    if error is None:
        try:
            # 连接远程文件夹
            remote_manager = RemoteFileManager(
                form.remote_path,
                form.username,
                form.password,
                form.domain
            )
            
            if not remote_manager.connect():
                return json_response(error='无法连接到远程路径')
            
            try:
                # 获取文件列表
                files = remote_manager.list_files('')
                
                # 保存连接信息
                remote_folder = RemoteFolder.objects.create(
                    name=form.remote_path.split('\\')[-1] or form.remote_path.split('/')[-1] or form.remote_path,
                    remote_path=form.remote_path,
                    username=form.username,
                    password=form.password,
                    domain=form.domain,
                    is_active=True
                )
                
                # 如果开启了自动缓存，则启动缓存任务
                if form.auto_cache:
                    try:
                        from apps.file.redis_cache_service import cache_service
                        # 使用多线程异步缓存整个文件树
                        threading.Thread(
                            target=cache_service.cache_entire_file_tree,
                            args=(remote_folder.id,),
                            daemon=True
                        ).start()
                    except Exception as e:
                        logger.error(f"启动缓存任务失败: {e}")
                
                return json_response(files)
                
            finally:
                remote_manager.disconnect()
                
        except Exception as e:
            return json_response(error=f'连接远程文件夹失败: {e}')
    return json_response(error=error)


@method_route(methods=['POST'])
def cache_remote_folder(request):
    """缓存远程文件夹"""
    form, error = JsonParser(
        Argument('remote_folder_id', type=int, help='请选择远程文件夹'),
        Argument('path', default=''),
        Argument('recursive', type=bool, default=True)
    ).parse(request.body)
    
    if error is None:
        try:
            # 导入缓存服务
            try:
                from apps.file.redis_cache_service import cache_service
            except ImportError as e:
                return json_response(error=f'缓存服务导入失败: {e}')
            except Exception as e:
                return json_response(error=f'缓存服务初始化失败: {e}')
            
            # 检查远程文件夹是否存在
            if not RemoteFolder.objects.filter(id=form.remote_folder_id, is_active=True).exists():
                return json_response(error=f'远程文件夹不存在: {form.remote_folder_id}')
            
            # 使用多线程异步缓存
            if form.recursive:
                # 启动异步任务
                thread = threading.Thread(
                    target=cache_service.cache_entire_file_tree,
                    args=(form.remote_folder_id,),
                    daemon=True
                )
                thread.start()
                return json_response('缓存任务已启动，请稍后查看')
            else:
                # 同步刷新单个路径
                success = cache_service.refresh_folder_cache(form.remote_folder_id, form.path, False)
                if success:
                    return json_response('缓存刷新成功')
                else:
                    return json_response(error='缓存刷新失败')
                
        except Exception as e:
            return json_response(error=f'缓存远程文件夹失败: {e}')
            
    return json_response(error=error)


@method_route(methods=['GET'])
def get_cache_status(request):
    """获取缓存状态"""
    try:
        # 导入缓存服务
        try:
            from apps.file.redis_cache_service import cache_service
        except ImportError as e:
            return json_response(error=f'缓存服务导入失败: {e}')
        except Exception as e:
            return json_response(error=f'缓存服务初始化失败: {e}')
        
        # 获取缓存状态
        status = cache_service.get_cache_status()
        return json_response(status)
        
    except Exception as e:
        return json_response(error=f'获取缓存状态失败: {e}')


# 定义远程文件夹相关的视图函数
@method_route(methods=['GET'])
def get_remote_folders(request):
    """获取远程文件夹列表"""
    try:
        remote_folders = RemoteFolder.objects.filter(is_active=True)
        return json_response([{
            'id': folder.id,
            'name': folder.name,
            'remote_path': folder.remote_path,
            'created_time': human_datetime(folder.created_time)
        } for folder in remote_folders])
    except Exception as e:
        return json_response(error=f'获取远程文件夹列表失败: {e}')

@method_route(methods=['GET'])
def get_remote_files(request):
    """获取远程文件列表"""
    form, error = JsonParser(
        Argument('folder_id', type=int, help='请选择远程文件夹'),
        Argument('path', default='')
    ).parse(request.GET)
    
    if error is None:
        try:
            # 导入缓存服务
            try:
                from apps.file.redis_cache_service import cache_service
                # 尝试从缓存获取文件列表
                files, is_from_cache = cache_service.get_cached_files(form.folder_id, form.path)
                return json_response(files)
            except ImportError:
                # 如果缓存服务不可用，直接获取文件列表
                remote_folder = RemoteFolder.objects.get(id=form.folder_id, is_active=True)
                remote_manager = RemoteFileManager(
                    remote_folder.remote_path,
                    remote_folder.username,
                    remote_folder.password,
                    remote_folder.domain
                )
                
                if not remote_manager.connect():
                    return json_response(error='无法连接到远程路径')
                
                try:
                    files = remote_manager.list_files(form.path)
                    return json_response(files)
                finally:
                    remote_manager.disconnect()
                    
        except RemoteFolder.DoesNotExist:
            return json_response(error=f'远程文件夹不存在: {form.folder_id}')
        except Exception as e:
            return json_response(error=f'获取远程文件列表失败: {e}')
    return json_response(error=error)

@method_route(methods=['GET'])
def download_remote_file(request):
    """下载远程文件"""
    form, error = JsonParser(
        Argument('folder_id', type=int, help='请选择远程文件夹'),
        Argument('path', default=''),
        Argument('file_name', help='请指定文件名')
    ).parse(request.GET)
    
    if error is None:
        try:
            remote_folder = RemoteFolder.objects.get(id=form.folder_id, is_active=True)
            remote_manager = RemoteFileManager(
                remote_folder.remote_path,
                remote_folder.username,
                remote_folder.password,
                remote_folder.domain
            )
            
            if not remote_manager.connect():
                return json_response(error='无法连接到远程路径')
            
            try:
                file_path = remote_manager.download_file(form.path, form.file_name)
                if file_path:
                    response = FileResponse(open(file_path, 'rb'))
                    response['Content-Type'] = 'application/octet-stream'
                    response['Content-Disposition'] = f'attachment; filename="{form.file_name}"'
                    return response
                else:
                    return json_response(error='下载文件失败')
            finally:
                remote_manager.disconnect()
                
        except RemoteFolder.DoesNotExist:
            return json_response(error=f'远程文件夹不存在: {form.folder_id}')
        except Exception as e:
            return json_response(error=f'下载文件失败: {e}')
    return json_response(error=error)

@method_route(methods=['POST'])
def upload_to_remote(request):
    """上传文件到远程文件夹"""
    form, error = JsonParser(
        Argument('folder_id', type=int, help='请选择远程文件夹'),
        Argument('path', default='')
    ).parse(request.POST)
    
    if error is None:
        try:
            if 'file' not in request.FILES:
                return json_response(error='请选择要上传的文件')
                
            file = request.FILES['file']
            
            remote_folder = RemoteFolder.objects.get(id=form.folder_id, is_active=True)
            remote_manager = RemoteFileManager(
                remote_folder.remote_path,
                remote_folder.username,
                remote_folder.password,
                remote_folder.domain
            )
            
            if not remote_manager.connect():
                return json_response(error='无法连接到远程路径')
            
            try:
                # 创建临时文件
                with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                    for chunk in file.chunks():
                        temp_file.write(chunk)
                    temp_file_path = temp_file.name
                
                # 上传文件
                success = remote_manager.upload_file(temp_file_path, form.path, file.name)
                
                # 删除临时文件
                os.unlink(temp_file_path)
                
                if success:
                    return json_response('上传成功')
                else:
                    return json_response(error='上传文件失败')
            finally:
                remote_manager.disconnect()
                
        except RemoteFolder.DoesNotExist:
            return json_response(error=f'远程文件夹不存在: {form.folder_id}')
        except Exception as e:
            return json_response(error=f'上传文件失败: {e}')
    return json_response(error=error)

# 添加一个新的API端点，用于将远程文件夹中的文件转移到本地文件夹
@method_route(methods=['POST'])
def transfer_to_local(request):
    """从远程文件夹转移文件到本地"""
    import platform
    import logging
    
    logger = logging.getLogger('apps.file')
    logger.info("开始处理远程文件转移请求")
    
    form, error = JsonParser(
        Argument('folder_id', type=int, help='请指定远程文件夹配置ID'),
        Argument('remote_file_path', help='请指定远程文件路径'),
        Argument('local_path', default='', required=False)
    ).parse(request.body)
    
    if error is None:
        try:
            folder_config = RemoteFolder.objects.get(id=form.folder_id, is_active=True)
            logger.info(f"获取到远程文件夹配置: ID={form.folder_id}, 路径={folder_config.remote_path}")
            
            # 获取远程文件名
            file_name = os.path.basename(form.remote_file_path)
            logger.info(f"待转移文件名: {file_name}")
            
            # 构建本地目标路径
            files_dir = os.path.join(settings.BASE_DIR, 'files')
            
            # 处理用户提供的本地路径，确保格式正确
            local_path = form.local_path.strip().replace('\\', '/').lstrip('/')
            local_target_dir = os.path.join(files_dir, local_path) if local_path else files_dir
            logger.info(f"本地目标目录: {local_target_dir}")
            
            # 安全检查
            if not os.path.abspath(local_target_dir).startswith(os.path.abspath(files_dir)):
                logger.error(f"非法的本地路径: {local_target_dir}")
                return json_response(error='非法的本地路径，必须在files目录内')
            
            # 确保本地目录存在
            if not os.path.exists(local_target_dir):
                try:
                    logger.info(f"创建本地目标目录: {local_target_dir}")
                    os.makedirs(local_target_dir, exist_ok=True)
                except Exception as e:
                    logger.error(f"创建目标文件夹失败: {str(e)}")
                    return json_response(error=f'创建目标文件夹失败: {str(e)}')
            
            local_file_path = os.path.join(local_target_dir, file_name)
            
            # 如果本地文件已存在，添加时间戳
            if os.path.exists(local_file_path):
                name, ext = os.path.splitext(file_name)
                timestamp = int(time.time())
                file_name = f"{name}_{timestamp}{ext}"
                local_file_path = os.path.join(local_target_dir, file_name)
                logger.info(f"本地文件已存在，重命名为: {file_name}")
            
            # 创建远程文件管理器
            logger.info(f"🔌 创建远程文件管理器")
            logger.info(f"  - 远程路径: {folder_config.remote_path}")
            logger.info(f"  - 用户名: {folder_config.username}")
            logger.info(f"  - 域名: {folder_config.domain}")
            
            remote_manager = RemoteFileManager(
                folder_config.remote_path,
                folder_config.username,
                folder_config.password,
                folder_config.domain
            )
            
            logger.info(f"🔗 尝试连接到远程文件夹...")
            if not remote_manager.connect():
                logger.error(f"❌ 无法连接到远程路径: {folder_config.remote_path}")
                return json_response(error='无法连接到远程路径')
            else:
                logger.info(f"✅ 成功连接到远程文件夹")
            
            try:
                # 简化的文件转移处理逻辑
                # 直接使用：远程根路径 + 相对路径
                remote_base_path = folder_config.remote_path
                file_name = os.path.basename(form.remote_file_path)
                
                # 构建完整的远程文件路径
                full_remote_path = os.path.join(remote_base_path, form.remote_file_path)
                if platform.system() == 'Windows':
                    full_remote_path = full_remote_path.replace('/', '\\')
                
                logger.info(f"📁 简化的文件转移:")
                logger.info(f"  - 远程根路径: {remote_base_path}")
                logger.info(f"  - 相对路径: {form.remote_file_path}")
                logger.info(f"  - 完整路径: {full_remote_path}")
                logger.info(f"  - 文件名: {file_name}")
                logger.info(f"  - 本地保存路径: {local_file_path}")
                
                # 直接尝试下载文件
                logger.info(f"🚀 开始下载文件...")
                if remote_manager.download_file(full_remote_path, local_file_path):
                    logger.info(f"✅ 文件下载成功!")
                else:
                    logger.error(f"❌ 文件下载失败: {full_remote_path}")
                    return json_response(error=f'转移文件失败: 文件路径不正确或文件不存在: {form.remote_file_path}')
                
                # 检查文件是否成功下载
                if not os.path.exists(local_file_path):
                    logger.error(f"下载后文件不存在: {local_file_path}")
                    return json_response(error=f'转移文件失败: 下载的文件不存在')
                
                if os.path.getsize(local_file_path) == 0:
                    logger.error(f"下载的文件为空: {local_file_path}")
                    return json_response(error=f'转移文件失败: 下载的文件为空')
                
                # 获取相对于files目录的路径，用于前端显示
                relative_path = os.path.relpath(local_file_path, files_dir)
                logger.info(f"文件转移成功: {relative_path}")
                
                return json_response({
                    'message': '文件转移成功',
                    'file_name': file_name,
                    'local_path': relative_path.replace('\\', '/'),
                    'full_path': local_file_path
                })
                
            finally:
                remote_manager.disconnect()
                
        except RemoteFolder.DoesNotExist:
            logger.error(f"远程文件夹不存在: {form.folder_id}")
            return json_response(error=f'远程文件夹不存在: {form.folder_id}')
        except Exception as e:
            logger.error(f"转移文件失败: {str(e)}")
            return json_response(error=f'转移文件失败: {str(e)}')
    return json_response(error=error)


@method_route(methods=['POST'])
def universal_file_transfer(request):
    """
    通用文件转移功能
    支持: 远程→本地, 本地→远程, 本地→本地, 远程→远程
    """
    import logging
    import tempfile
    import platform
    import shutil
    
    logger = logging.getLogger('apps.file')
    
    try:
        form = json.loads(request.body)
        logger.info(f"🔄 通用文件转移请求: {form}")
        
        transfer_type = form.get('transfer_type')
        source_type = form.get('source_type')
        source_folder_id = form.get('source_folder_id')
        source_file_path = form.get('source_file_path')
        target_type = form.get('target_type')
        target_folder_id = form.get('target_folder_id')
        target_local_path = form.get('target_local_path', '')
        file_name = form.get('file_name')
        
        if not all([transfer_type, source_type, source_file_path, target_type, file_name]):
            return json_response(error='缺少必要参数')
        
        logger.info(f"📋 转移参数:")
        logger.info(f"  - 转移类型: {transfer_type}")
        logger.info(f"  - 源类型: {source_type}, 源文件夹ID: {source_folder_id}, 源路径: {source_file_path}")
        logger.info(f"  - 目标类型: {target_type}, 目标文件夹ID: {target_folder_id}, 目标本地路径: {target_local_path}")
        logger.info(f"  - 文件名: {file_name}")
        
        # 根据转移类型执行不同逻辑
        if transfer_type == 'remote_to_local':
            return _handle_remote_to_local(source_folder_id, source_file_path, target_local_path, file_name)
        elif transfer_type == 'local_to_remote':
            return _handle_local_to_remote(source_file_path, target_folder_id, file_name)
        elif transfer_type == 'local_to_local':
            return _handle_local_to_local(source_file_path, target_local_path, file_name)
        elif transfer_type == 'remote_to_remote':
            return _handle_remote_to_remote(source_folder_id, source_file_path, target_folder_id, file_name)
        else:
            return json_response(error=f'不支持的转移类型: {transfer_type}')
            
    except json.JSONDecodeError:
        return json_response(error='请求参数格式错误')
    except Exception as e:
        logger.error(f"通用文件转移失败: {str(e)}")
        return json_response(error=f'转移失败: {str(e)}')

def _handle_remote_to_local(source_folder_id, source_file_path, target_local_path, file_name):
    """处理远程到本地的转移"""
    import logging
    import platform
    
    logger = logging.getLogger('apps.file')
    logger.info("🌐→📁 执行远程到本地转移")
    
    try:
        # 获取远程文件夹配置
        folder_config = RemoteFolder.objects.get(id=source_folder_id)
        logger.info(f"远程文件夹配置: {folder_config.name} - {folder_config.remote_path}")
        
        # 创建远程文件管理器
        remote_manager = RemoteFileManager(
            folder_config.remote_path,
            folder_config.username,
            folder_config.password,
            folder_config.domain
        )
        
        if not remote_manager.connect():
            return json_response(error='无法连接到远程文件夹')
        
        # 构建本地目标路径
        local_base_path = os.path.join(settings.BASE_DIR, 'files')
        if target_local_path:
            local_target_dir = os.path.join(local_base_path, target_local_path)
        else:
            local_target_dir = local_base_path
            
        # 确保目标目录存在
        os.makedirs(local_target_dir, exist_ok=True)
        local_file_path = os.path.join(local_target_dir, file_name)
        
        # 构建远程文件完整路径
        remote_base_path = folder_config.remote_path
        remote_file_path = os.path.join(remote_base_path, source_file_path)
        if platform.system() == 'Windows':
            remote_file_path = remote_file_path.replace('/', '\\')
        
        logger.info(f"从 {remote_file_path} 下载到 {local_file_path}")
        
        # 下载文件
        if remote_manager.download_file(remote_file_path, local_file_path):
            logger.info("✅ 远程到本地转移成功")
            return json_response(data={'message': f'文件 {file_name} 已成功从远程下载到本地'})
        else:
            return json_response(error='下载文件失败')
            
    except RemoteFolder.DoesNotExist:
        return json_response(error='远程文件夹配置不存在')
    except Exception as e:
        logger.error(f"远程到本地转移失败: {str(e)}")
        return json_response(error=f'转移失败: {str(e)}')

def _handle_local_to_remote(source_file_path, target_folder_id, file_name):
    """处理本地到远程的转移"""
    import logging
    import platform
    
    logger = logging.getLogger('apps.file')
    logger.info("📁→🌐 执行本地到远程转移")
    
    try:
        # 获取远程文件夹配置
        folder_config = RemoteFolder.objects.get(id=target_folder_id)
        logger.info(f"目标远程文件夹: {folder_config.name} - {folder_config.remote_path}")
        
        # 构建源文件路径
        local_base_path = os.path.join(settings.BASE_DIR, 'files')
        source_full_path = os.path.join(local_base_path, source_file_path)
        logger.info(f"检查源文件路径: {source_full_path}")
        
        # 如果源文件不存在，尝试智能查找
        if not os.path.exists(source_full_path):
            logger.warning(f"源文件路径不存在: {source_full_path}")
            
            # 尝试在各个子目录中查找文件
            found_file_path = None
            for root, dirs, files in os.walk(local_base_path):
                if file_name in files:
                    potential_path = os.path.join(root, file_name)
                    logger.info(f"找到潜在文件: {potential_path}")
                    found_file_path = potential_path
                    break
            
            if found_file_path:
                logger.info(f"智能查找成功，使用找到的文件: {found_file_path}")
                source_full_path = found_file_path
            else:
                logger.error(f"智能查找失败，文件 {file_name} 在 {local_base_path} 及其子目录中都不存在")
                return json_response(error=f'源文件不存在: {source_file_path}，也无法在其他位置找到文件 {file_name}')
        
        # 创建远程文件管理器
        remote_manager = RemoteFileManager(
            folder_config.remote_path,
            folder_config.username,
            folder_config.password,
            folder_config.domain
        )
        
        if not remote_manager.connect():
            return json_response(error='无法连接到远程文件夹')
        
        # 构建远程目标路径
        remote_target_path = os.path.join(folder_config.remote_path, file_name)
        if platform.system() == 'Windows':
            remote_target_path = remote_target_path.replace('/', '\\')
        
        logger.info(f"从 {source_full_path} 上传到 {remote_target_path}")
        
        # 上传文件
        if remote_manager.upload_file(source_full_path, remote_target_path):
            logger.info("✅ 本地到远程转移成功")
            return json_response(data={'message': f'文件 {file_name} 已成功上传到远程文件夹'})
        else:
            return json_response(error='上传文件失败')
            
    except RemoteFolder.DoesNotExist:
        return json_response(error='远程文件夹配置不存在')
    except Exception as e:
        logger.error(f"本地到远程转移失败: {str(e)}")
        return json_response(error=f'转移失败: {str(e)}')

def _handle_local_to_local(source_file_path, target_local_path, file_name):
    """处理本地到本地的转移"""
    import logging
    import shutil
    
    logger = logging.getLogger('apps.file')
    logger.info("📁→📁 执行本地到本地转移")
    
    try:
        local_base_path = os.path.join(settings.BASE_DIR, 'files')
        
        # 构建源文件路径
        source_full_path = os.path.join(local_base_path, source_file_path)
        logger.info(f"检查源文件路径: {source_full_path}")
        
        # 如果源文件不存在，尝试智能查找
        if not os.path.exists(source_full_path):
            logger.warning(f"源文件路径不存在: {source_full_path}")
            
            # 尝试在各个子目录中查找文件
            found_file_path = None
            for root, dirs, files in os.walk(local_base_path):
                if file_name in files:
                    potential_path = os.path.join(root, file_name)
                    logger.info(f"找到潜在文件: {potential_path}")
                    found_file_path = potential_path
                    break
            
            if found_file_path:
                logger.info(f"智能查找成功，使用找到的文件: {found_file_path}")
                source_full_path = found_file_path
            else:
                logger.error(f"智能查找失败，文件 {file_name} 在 {local_base_path} 及其子目录中都不存在")
                return json_response(error=f'源文件不存在: {source_file_path}，也无法在其他位置找到文件 {file_name}')
        
        # 构建目标路径
        if target_local_path:
            target_dir = os.path.join(local_base_path, target_local_path)
        else:
            target_dir = local_base_path
        
        # 确保目标目录存在
        os.makedirs(target_dir, exist_ok=True)
        target_full_path = os.path.join(target_dir, file_name)
        
        # 检查是否是同一个文件
        if os.path.abspath(source_full_path) == os.path.abspath(target_full_path):
            return json_response(error='源文件和目标文件是同一个文件')
        
        logger.info(f"从 {source_full_path} 移动到 {target_full_path}")
        
        # 移动文件
        shutil.move(source_full_path, target_full_path)
        
        logger.info("✅ 本地到本地转移成功")
        return json_response(data={'message': f'文件 {file_name} 已成功移动到目标文件夹'})
        
    except Exception as e:
        logger.error(f"本地到本地转移失败: {str(e)}")
        return json_response(error=f'转移失败: {str(e)}')

def _handle_remote_to_remote(source_folder_id, source_file_path, target_folder_id, file_name):
    """处理远程到远程的转移"""
    import logging
    import tempfile
    import platform
    
    logger = logging.getLogger('apps.file')
    logger.info("🌐→🌐 执行远程到远程转移")
    
    try:
        # 获取源和目标远程文件夹配置
        source_folder = RemoteFolder.objects.get(id=source_folder_id)
        target_folder = RemoteFolder.objects.get(id=target_folder_id)
        
        logger.info(f"源远程文件夹: {source_folder.name} - {source_folder.remote_path}")
        logger.info(f"目标远程文件夹: {target_folder.name} - {target_folder.remote_path}")
        
        # 创建临时文件路径
        temp_dir = tempfile.mkdtemp()
        temp_file_path = os.path.join(temp_dir, file_name)
        
        try:
            # 步骤1: 从源远程文件夹下载到临时位置
            source_manager = RemoteFileManager(
                source_folder.remote_path,
                source_folder.username,
                source_folder.password,
                source_folder.domain
            )
            
            if not source_manager.connect():
                return json_response(error='无法连接到源远程文件夹')
            
            # 构建源文件完整路径
            source_remote_path = os.path.join(source_folder.remote_path, source_file_path)
            if platform.system() == 'Windows':
                source_remote_path = source_remote_path.replace('/', '\\')
            
            logger.info(f"步骤1: 从源远程下载 {source_remote_path} 到临时文件 {temp_file_path}")
            if not source_manager.download_file(source_remote_path, temp_file_path):
                return json_response(error='从源远程文件夹下载文件失败')
            
            # 步骤2: 从临时位置上传到目标远程文件夹
            target_manager = RemoteFileManager(
                target_folder.remote_path,
                target_folder.username,
                target_folder.password,
                target_folder.domain
            )
            
            if not target_manager.connect():
                return json_response(error='无法连接到目标远程文件夹')
            
            # 构建目标文件路径
            target_remote_path = os.path.join(target_folder.remote_path, file_name)
            if platform.system() == 'Windows':
                target_remote_path = target_remote_path.replace('/', '\\')
            
            logger.info(f"步骤2: 从临时文件 {temp_file_path} 上传到目标远程 {target_remote_path}")
            if not target_manager.upload_file(temp_file_path, target_remote_path):
                return json_response(error='上传到目标远程文件夹失败')
            
            logger.info("✅ 远程到远程转移成功")
            return json_response(data={'message': f'文件 {file_name} 已成功在远程文件夹间转移'})
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            os.rmdir(temp_dir)
            
    except RemoteFolder.DoesNotExist:
        return json_response(error='远程文件夹配置不存在')
    except Exception as e:
        logger.error(f"远程到远程转移失败: {str(e)}")
        return json_response(error=f'转移失败: {str(e)}')
