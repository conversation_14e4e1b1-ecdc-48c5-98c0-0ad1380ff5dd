import React, { useState, useEffect } from 'react';
import { Modal, Table, Button, Input, message, Space, Tag, Tooltip, Divider } from 'antd';
import { 
  WechatOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  SendOutlined,
  InfoCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { http } from 'libs';

const { TextArea } = Input;

/**
 * 企业微信配置管理弹窗
 */
export default function WeChatConfigModal({ visible, onCancel }) {
  const [loading, setLoading] = useState(false);
  const [configData, setConfigData] = useState([]);
  const [testVisible, setTestVisible] = useState(false);
  const [testWebhook, setTestWebhook] = useState('');
  const [testMessage, setTestMessage] = useState('这是一条来自Spug模型测试管理平台的测试消息');
  const [testLoading, setTestLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      fetchConfigData();
    }
  }, [visible]);

  const fetchConfigData = async () => {
    setLoading(true);
    try {
      const response = await http.get('/api/model-storage/wechat/config/');
      setConfigData(response.data || []);
    } catch (error) {
      console.error('获取企业微信配置失败:', error);
      message.error('获取配置数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTestSend = async () => {
    if (!testWebhook.trim()) {
      message.error('请输入企业微信Webhook地址');
      return;
    }

    setTestLoading(true);
    try {
      const response = await http.post('/api/model-storage/wechat/config/', {
        webhook_url: testWebhook,
        test_message: testMessage
      });

      if (response.error) {
        message.error(response.error);
      } else {
        message.success(response.message || '测试消息发送成功！');
        setTestVisible(false);
        setTestWebhook('');
        setTestMessage('这是一条来自Spug模型测试管理平台的测试消息');
      }
    } catch (error) {
      console.error('测试发送失败:', error);
      message.error('测试发送失败，请检查网络连接');
    } finally {
      setTestLoading(false);
    }
  };

  const columns = [
    {
      title: '用户信息',
      dataIndex: 'nickname',
      width: 150,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#999' }}>@{record.username}</div>
        </div>
      )
    },
    {
      title: '配置状态',
      dataIndex: 'has_wechat_config',
      width: 120,
      render: (hasConfig) => (
        hasConfig ? (
          <Tag icon={<CheckCircleOutlined />} color="success">
            已配置
          </Tag>
        ) : (
          <Tag icon={<ExclamationCircleOutlined />} color="warning">
            未配置
          </Tag>
        )
      )
    },
    {
      title: 'Webhook地址',
      dataIndex: 'wechat_webhook',
      ellipsis: true,
      render: (webhook) => (
        webhook ? (
          <Tooltip title={webhook}>
            <span style={{ color: '#1890ff' }}>
              {webhook.substring(0, 50)}...
            </span>
          </Tooltip>
        ) : (
          <span style={{ color: '#999' }}>未配置</span>
        )
      )
    }
  ];

  const configuredCount = configData.filter(item => item.has_wechat_config).length;
  const totalCount = configData.length;

  return (
    <>
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <WechatOutlined style={{ color: '#1890ff' }} />
            <span>企业微信配置管理</span>
          </div>
        }
        visible={visible}
        onCancel={onCancel}
        width={800}
        footer={[
          <Button key="test" icon={<SendOutlined />} onClick={() => setTestVisible(true)}>
            测试发送
          </Button>,
          <Button key="close" onClick={onCancel}>
            关闭
          </Button>
        ]}
      >
        <div style={{ marginBottom: '16px' }}>
          <div style={{ 
            padding: '12px', 
            backgroundColor: '#f0f9ff', 
            borderRadius: '6px',
            border: '1px solid #bae7ff'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
              <InfoCircleOutlined style={{ color: '#1890ff' }} />
              <span style={{ fontWeight: 500, color: '#1890ff' }}>配置统计</span>
            </div>
            <div style={{ fontSize: '14px', color: '#666' }}>
              总用户数: <strong>{totalCount}</strong> | 
              已配置: <strong style={{ color: '#52c41a' }}>{configuredCount}</strong> | 
              未配置: <strong style={{ color: '#faad14' }}>{totalCount - configuredCount}</strong>
            </div>
          </div>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <div style={{ 
            padding: '12px', 
            backgroundColor: '#fff7e6', 
            borderRadius: '6px',
            border: '1px solid #ffd591'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
              <SettingOutlined style={{ color: '#fa8c16' }} />
              <span style={{ fontWeight: 500, color: '#fa8c16' }}>配置说明</span>
            </div>
            <div style={{ fontSize: '12px', color: '#666', lineHeight: '1.5' }}>
              • 企业微信配置需要在 <strong>系统管理 → 报警管理 → 报警联系人</strong> 中设置<br />
              • 每个用户需要配置对应的企业微信机器人Webhook地址<br />
              • 配置完成后，任务分配时会自动发送企业微信通知<br />
              • 建议使用测试功能验证配置是否正确
            </div>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={configData}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          size="middle"
        />
      </Modal>

      {/* 测试发送弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <SendOutlined style={{ color: '#1890ff' }} />
            <span>测试企业微信消息发送</span>
          </div>
        }
        visible={testVisible}
        onOk={handleTestSend}
        onCancel={() => {
          setTestVisible(false);
          setTestWebhook('');
          setTestMessage('这是一条来自Spug模型测试管理平台的测试消息');
        }}
        confirmLoading={testLoading}
        okText="发送测试消息"
        cancelText="取消"
        width={600}
      >
        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 500 }}>
            企业微信Webhook地址 <span style={{ color: '#ff4d4f' }}>*</span>
          </label>
          <Input
            value={testWebhook}
            onChange={(e) => setTestWebhook(e.target.value)}
            placeholder="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx"
            style={{ width: '100%' }}
          />
          <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
            请输入企业微信机器人的Webhook地址
          </div>
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 500 }}>
            测试消息内容
          </label>
          <TextArea
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            rows={4}
            placeholder="输入要发送的测试消息内容"
            maxLength={500}
            showCount
          />
        </div>
      </Modal>
    </>
  );
}
