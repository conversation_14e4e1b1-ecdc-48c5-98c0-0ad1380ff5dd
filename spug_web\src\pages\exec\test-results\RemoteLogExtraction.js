/**
 * 远程日志提取组件
 * 用于从远程服务器提取日志文件并自动分析性能指标
 */
import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Button,
  Card,
  Steps,
  Space,
  Tag,
  Progress,
  message,
  Alert,
  Table,
  Spin,
  Row,
  Col,
  Statistic,
  Typography
} from 'antd';
import {
  CloudDownloadOutlined,
  SearchOutlined,
  ExperimentOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import http from 'libs/http';
import HostSelector from '../../host/Selector';
import styles from './RemoteLogExtraction.module.less';

const { Step } = Steps;
const { Text } = Typography;

function RemoteLogExtraction({ visible, onCancel, onSuccess }) {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);

  const [extractedMetrics, setExtractedMetrics] = useState([]);
  const [extractionProgress, setExtractionProgress] = useState(0);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [selectedHostIds, setSelectedHostIds] = useState([]);

  // 重置状态
  const resetState = () => {
    setCurrentStep(0);
    setExtractedMetrics([]);
    setExtractionProgress(0);
    setAnalysisResult(null);
    setSelectedHostIds([]);
    form.resetFields();
  };

  // 处理主机选择
  const handleHostSelect = (hostIds) => {
    setSelectedHostIds(hostIds);
    // 只取第一个主机ID，因为我们只支持单主机
    const hostId = hostIds.length > 0 ? hostIds[0] : null;
    form.setFieldsValue({ hostId });
  };

  // 步骤1: 从远程服务器获取日志文件
  const handleFetchLog = async () => {
    try {
      const values = await form.validateFields(['logPath', 'planName', 'hostId']);
      setLoading(true);
      setExtractionProgress(10);

      // 通过SSH连接获取远程日志文件
      const response = await http.post('/api/exec/remote-log-fetch/', {
        log_path: values.logPath,
        plan_name: values.planName,
        host_id: values.hostId
      });

      if (response.success) {
        setExtractionProgress(50);
        setCurrentStep(1);
        message.success('日志文件获取成功');

        // 自动开始分析
        setTimeout(() => performAnalysis(response.content, values.planName), 500);
      } else {
        throw new Error(response.error || '获取日志文件失败');
      }
    } catch (error) {
      message.error('获取日志文件失败: ' + (error.response?.data?.error || error.message));
      setExtractionProgress(0);
    } finally {
      setLoading(false);
    }
  };

  // 步骤2: 自动分析日志内容，提取性能指标
  const performAnalysis = async (content, planName) => {
    try {
      setLoading(true);
      setExtractionProgress(60);

      // 使用智能算法分析日志内容
      const metrics = analyzeLogContent(content);
      setExtractedMetrics(metrics);
      setExtractionProgress(90);

      // 生成分析结果摘要
      const summary = {
        totalMetrics: metrics.length,
        highConfidenceMetrics: metrics.filter(m => m.confidence > 0.8).length,
        categories: [...new Set(metrics.map(m => m.category))],
        planName
      };
      setAnalysisResult(summary);

      setExtractionProgress(100);
      setCurrentStep(2);
      message.success(`分析完成，发现 ${metrics.length} 个性能指标`);
    } catch (error) {
      message.error('日志分析失败: ' + error.message);
      setExtractionProgress(60);
    } finally {
      setLoading(false);
    }
  };

  // 增强的日志内容分析函数
  const analyzeLogContent = (content) => {
    const metrics = [];
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      // 模式1: 成功请求数等计数指标
      const countPattern = /([^:]+):\s*(\d+)\s*$/;
      const countMatch = line.match(countPattern);
      if (countMatch && parseFloat(countMatch[2]) > 0) {
        const label = countMatch[1].trim();
        if (label.toLowerCase().includes('request') || 
            label.toLowerCase().includes('token') ||
            label.toLowerCase().includes('duration')) {
          metrics.push({
            id: `metric_${index}_${Math.random()}`,
            label: label,
            value: countMatch[2],
            unit: getUnitFromLabel(label),
            confidence: 0.95,
            category: getCategoryFromLabel(label),
            lineNumber: index + 1,
            originalText: line.trim()
          });
        }
      }

      // 模式2: 带小数的性能指标
      const performancePattern = /([^:]+):\s*([0-9.,]+)\s*([A-Za-z/°%]+)?/;
      const perfMatch = line.match(performancePattern);
      if (perfMatch && parseFloat(perfMatch[2].replace(',', '')) > 0) {
        const label = perfMatch[1].trim();
        const value = perfMatch[2].replace(',', '');
        const unit = perfMatch[3] || getUnitFromLabel(label);
        
        if (isPerformanceMetric(label)) {
          metrics.push({
            id: `metric_${index}_${Math.random()}`,
            label: label,
            value: value,
            unit: unit,
            confidence: 0.90,
            category: getCategoryFromLabel(label),
            lineNumber: index + 1,
            originalText: line.trim()
          });
        }
      }

      // 模式3: GPU硬件信息
      const hardwarePattern = /(GPU Model|Memory Usage|GPU Temperature|Power Consumption|Model Size|Batch Size):\s*(.+)/;
      const hwMatch = line.match(hardwarePattern);
      if (hwMatch) {
        metrics.push({
          id: `metric_${index}_${Math.random()}`,
          label: hwMatch[1],
          value: hwMatch[2].replace(/[^\d.]/g, ''),
          unit: extractUnitFromValue(hwMatch[2]),
          confidence: 0.95,
          category: 'Hardware',
          lineNumber: index + 1,
          originalText: line.trim()
        });
      }
    });

    return metrics;
  };

  // 辅助函数
  const getUnitFromLabel = (label) => {
    const lowerLabel = label.toLowerCase();
    if (lowerLabel.includes('time') || lowerLabel.includes('latency') || lowerLabel.includes('ttft') || lowerLabel.includes('tpot') || lowerLabel.includes('itl')) {
      return 'ms';
    }
    if (lowerLabel.includes('throughput') || lowerLabel.includes('rate')) {
      return '/s';
    }
    if (lowerLabel.includes('token')) {
      return 'tokens';
    }
    if (lowerLabel.includes('request')) {
      return '次';
    }
    if (lowerLabel.includes('duration')) {
      return 's';
    }
    return '';
  };

  const getCategoryFromLabel = (label) => {
    const lowerLabel = label.toLowerCase();
    if (lowerLabel.includes('ttft') || lowerLabel.includes('time to first token')) {
      return 'Latency';
    }
    if (lowerLabel.includes('tpot') || lowerLabel.includes('time per output token')) {
      return 'Latency';
    }
    if (lowerLabel.includes('itl') || lowerLabel.includes('inter-token latency')) {
      return 'Latency';
    }
    if (lowerLabel.includes('throughput') || lowerLabel.includes('rate')) {
      return 'Throughput';
    }
    if (lowerLabel.includes('request')) {
      return 'Request';
    }
    if (lowerLabel.includes('token')) {
      return 'Token';
    }
    if (lowerLabel.includes('duration')) {
      return 'Time';
    }
    return 'General';
  };

  const isPerformanceMetric = (label) => {
    const keywords = ['ttft', 'tpot', 'itl', 'throughput', 'latency', 'duration', 'time', 'rate', 'mean', 'median', 'p99'];
    return keywords.some(keyword => label.toLowerCase().includes(keyword));
  };

  const extractUnitFromValue = (value) => {
    const unitMatch = value.match(/([A-Za-z°%/]+)/);
    return unitMatch ? unitMatch[1] : '';
  };

  // 步骤3: 保存提取结果
  const handleSaveResults = async () => {
    if (extractedMetrics.length === 0) {
      message.warning('没有发现可提取的性能指标');
      return;
    }

    try {
      setLoading(true);
      const values = form.getFieldsValue();
      
      const resultData = {
        plan_name: values.planName,
        log_path: values.logPath,
        metrics: extractedMetrics.map(metric => ({
          label: metric.label,
          value: metric.value,
          unit: metric.unit,
          confidence: metric.confidence,
          category: metric.category
        })),
        extraction_time: new Date().toISOString(),
        total_metrics: extractedMetrics.length,
        source: 'remote_extraction'
      };

      await http.post('/api/exec/test-results/', resultData);
      message.success(`成功保存 ${extractedMetrics.length} 个性能指标`);
      onSuccess();
    } catch (error) {
      message.error('保存失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 指标表格列定义
  const metricColumns = [
    {
      title: '指标名称',
      dataIndex: 'label',
      key: 'label',
      width: 200,
    },
    {
      title: '数值',
      dataIndex: 'value',
      key: 'value',
      width: 120,
      render: (value, record) => `${value} ${record.unit}`
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category) => <Tag color="blue">{category}</Tag>
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      width: 100,
      render: (confidence) => (
        <Progress 
          percent={Math.round(confidence * 100)} 
          size="small" 
          status={confidence > 0.8 ? 'success' : 'normal'}
        />
      )
    },
    {
      title: '行号',
      dataIndex: 'lineNumber',
      key: 'lineNumber',
      width: 80,
    }
  ];

  return (
    <Modal
      title="远程日志提取"
      visible={visible}
      onCancel={() => {
        onCancel();
        resetState();
      }}
      width={1000}
      style={{ top: 20 }}
      footer={null}
      className={styles.remoteExtractionModal}
    >
      <div className={styles.container}>
        {/* 步骤指示器 */}
        <Steps current={currentStep} className={styles.steps}>
          <Step 
            title="获取日志" 
            description="从远程服务器获取日志文件"
            icon={<CloudDownloadOutlined />}
          />
          <Step 
            title="智能分析" 
            description="自动分析并提取性能指标"
            icon={<ExperimentOutlined />}
          />
          <Step 
            title="保存结果" 
            description="确认并保存提取的指标"
            icon={<SaveOutlined />}
          />
        </Steps>

        {/* 进度指示器 */}
        {extractionProgress > 0 && (
          <Progress 
            percent={extractionProgress} 
            className={styles.progress}
            strokeColor="#722ed1"
          />
        )}

        {/* 步骤1: 日志获取配置 */}
        {currentStep === 0 && (
          <Card title="配置日志获取参数" className={styles.stepCard}>
            <Form form={form} layout="vertical">
              <Form.Item
                name="hostId"
                label="目标主机"
                rules={[{ required: true, message: '请选择目标主机' }]}
              >
                <HostSelector
                  type="button"
                  value={selectedHostIds}
                  onChange={handleHostSelect}
                  onlyOne={true}
                  title="选择目标主机"
                />
              </Form.Item>

              <Form.Item
                name="logPath"
                label="远程日志文件路径"
                rules={[{ required: true, message: '请输入日志文件路径' }]}
                initialValue="/opt/il1024_ol31744_np16_mc2.log"
              >
                <Input
                  placeholder="例如: /opt/il1024_ol31744_np16_mc2.log"
                  prefix={<FileTextOutlined />}
                />
              </Form.Item>

              <Form.Item
                name="planName"
                label="测试计划名称"
                rules={[{ required: true, message: '请输入测试计划名称' }]}
              >
                <Input placeholder="请输入测试计划名称" />
              </Form.Item>

              <Alert
                message="提示"
                description="请先选择目标主机，系统将通过SSH连接到该主机获取指定路径的日志文件，然后自动分析其中的性能指标。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            </Form>

            <div className={styles.stepActions}>
              <Button 
                type="primary"
                onClick={handleFetchLog}
                loading={loading}
                icon={<SearchOutlined />}
              >
                获取并分析日志
              </Button>
            </div>
          </Card>
        )}

        {/* 步骤2: 分析结果展示 */}
        {currentStep === 1 && (
          <Card title="日志分析中..." className={styles.stepCard}>
            <Spin spinning={loading}>
              <Alert
                message="正在分析日志内容"
                description="AI正在自动识别日志中的性能指标，请稍候..."
                type="info"
                showIcon
              />
            </Spin>
          </Card>
        )}

        {/* 步骤3: 结果确认和保存 */}
        {currentStep === 2 && (
          <div>
            {/* 分析结果摘要 */}
            <Card title="分析结果摘要" className={styles.summaryCard}>
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic 
                    title="发现指标" 
                    value={analysisResult?.totalMetrics || 0} 
                    prefix={<ExperimentOutlined />}
                  />
                </Col>
                <Col span={6}>
                  <Statistic 
                    title="高置信度" 
                    value={analysisResult?.highConfidenceMetrics || 0} 
                    prefix={<CheckCircleOutlined />}
                  />
                </Col>
                <Col span={6}>
                  <Statistic 
                    title="指标类别" 
                    value={analysisResult?.categories?.length || 0} 
                  />
                </Col>
                <Col span={6}>
                  <Text strong>
                    {analysisResult?.categories?.map(cat => (
                      <Tag key={cat} color="blue">{cat}</Tag>
                    ))}
                  </Text>
                </Col>
              </Row>
            </Card>

            {/* 提取的指标列表 */}
            <Card title="提取的性能指标" className={styles.metricsCard}>
              <Table
                dataSource={extractedMetrics}
                columns={metricColumns}
                rowKey="id"
                pagination={false}
                size="small"
                scroll={{ y: 300 }}
              />
            </Card>

            {/* 操作按钮 */}
            <div className={styles.stepActions}>
              <Space>
                <Button 
                  onClick={() => {
                    setCurrentStep(0);
                    setExtractedMetrics([]);
                    setExtractionProgress(0);
                  }}
                  icon={<ReloadOutlined />}
                >
                  重新提取
                </Button>
                <Button 
                  type="primary"
                  onClick={handleSaveResults}
                  loading={loading}
                  icon={<SaveOutlined />}
                  disabled={extractedMetrics.length === 0}
                >
                  保存结果 ({extractedMetrics.length})
                </Button>
              </Space>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}

export default RemoteLogExtraction;