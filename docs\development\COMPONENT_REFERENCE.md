# Spug Frontend Component Reference

## Table of Contents

1. [Core Components](#core-components)
2. [Authentication Components](#authentication-components)
3. [Upload Components](#upload-components)
4. [Editor Components](#editor-components)
5. [Layout Components](#layout-components)
6. [Utility Components](#utility-components)

---

## Core Components

### TableCard Component

**Location**: `src/components/TableCard.js`

A powerful table component with built-in search, filtering, column management, and batch operations.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `title` | `string` | - | Table title displayed in header |
| `columns` | `array` | `[]` | Ant Design table column definitions |
| `dataSource` | `array` | `[]` | Table data array |
| `actions` | `array` | `[]` | Action buttons in header |
| `batchActions` | `array` | `[]` | Batch operation buttons for selected rows |
| `selected` | `array` | `[]` | Currently selected row keys |
| `onReload` | `function` | - | Callback for reload button |
| `rowSelection` | `object` | - | Ant Design row selection config |
| `pagination` | `object/boolean` | - | Pagination configuration |
| `loading` | `boolean` | `false` | Loading state |
| `rowKey` | `string/function` | `'id'` | Row key property or function |
| `tKey` | `string` | - | Unique key for persisting column preferences |
| `scroll` | `object` | - | Table scroll configuration |
| `tableLayout` | `string` | - | Table layout (`'fixed'` or `'auto'`) |
| `expandable` | `object` | - | Expandable row configuration |

#### Sub-Components

##### TableCard.Search

Search component for table filtering.

**Props**:
- `keys` (array): Search field options in format `['field/Label', ...]`
- `onChange` (function): Callback `(key, value) => {}`

#### Usage Examples

##### Basic Table
```jsx
import { TableCard } from '@/components';

function UserList() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);

  const columns = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
    { title: 'Name', dataIndex: 'name', key: 'name' },
    { title: 'Email', dataIndex: 'email', key: 'email' },
    { title: 'Status', dataIndex: 'is_active', key: 'status',
      render: (active) => active ? 'Active' : 'Inactive' },
    { title: 'Actions', key: 'actions',
      render: (_, record) => (
        <Space>
          <Button size="small" onClick={() => handleEdit(record)}>Edit</Button>
          <Button size="small" danger onClick={() => handleDelete(record)}>Delete</Button>
        </Space>
      )}
  ];

  return (
    <TableCard
      title="User Management"
      columns={columns}
      dataSource={users}
      loading={loading}
      rowKey="id"
      tKey="user-list" // For column preference persistence
      onReload={fetchUsers}
      actions={[
        <Button key="add" type="primary" onClick={handleAdd}>Add User</Button>
      ]}
    />
  );
}
```

##### Table with Search and Batch Operations
```jsx
function HostList() {
  const [hosts, setHosts] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [searchParams, setSearchParams] = useState({});

  const rowSelection = {
    selectedRowKeys: selectedKeys,
    onChange: setSelectedKeys,
  };

  const batchActions = [
    <Button key="batch-delete" danger onClick={handleBatchDelete}>
      Delete Selected ({selectedKeys.length})
    </Button>,
    <Button key="batch-verify" onClick={handleBatchVerify}>
      Verify Selected
    </Button>
  ];

  const handleSearch = (key, value) => {
    setSearchParams({ ...searchParams, [key]: value });
    // Implement search logic
  };

  return (
    <TableCard
      title="Host Management"
      columns={columns}
      dataSource={hosts}
      rowSelection={rowSelection}
      selected={selectedKeys}
      batchActions={batchActions}
      actions={[
        <TableCard.Search
          key="search"
          keys={['name/Name', 'hostname/IP Address', 'desc/Description']}
          onChange={handleSearch}
        />,
        <Button key="add" type="primary">Add Host</Button>
      ]}
    />
  );
}
```

---

## Authentication Components

### AuthButton Component

**Location**: `src/components/AuthButton.js`

Button component with built-in permission checking. Renders `null` if user lacks required permissions.

#### Props

All Ant Design Button props plus:

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `auth` | `string` | - | Required permission string |

#### Usage Examples

```jsx
import { AuthButton } from '@/components';

// Basic usage
<AuthButton auth="host.host.add" type="primary">
  Add Host
</AuthButton>

// Multiple permissions (OR)
<AuthButton auth="host.host.add|host.host.edit" type="primary">
  Manage Host
</AuthButton>

// Multiple permissions (AND)
<AuthButton auth="host.host.view&system.account.view" type="default">
  Admin Panel
</AuthButton>

// With other Button props
<AuthButton 
  auth="deploy.app.deploy" 
  type="primary" 
  size="large"
  loading={deploying}
  onClick={handleDeploy}
>
  Deploy Application
</AuthButton>
```

### AuthCard Component

**Location**: `src/components/AuthCard.js`

Card wrapper with permission checking.

#### Props

All Ant Design Card props plus:

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `auth` | `string` | - | Required permission string |

### AuthDiv Component

**Location**: `src/components/AuthDiv.js`

Generic wrapper div with permission checking.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `auth` | `string` | - | Required permission string |
| `children` | `ReactNode` | - | Child components |

#### Usage Examples

```jsx
// Conditional rendering of entire sections
<AuthDiv auth="system.account.view">
  <Card title="User Management">
    <UserList />
  </Card>
</AuthDiv>

// Conditional menu items
<Menu>
  <Menu.Item key="dashboard">Dashboard</Menu.Item>
  <AuthDiv auth="host.host.view">
    <Menu.Item key="hosts">Host Management</Menu.Item>
  </AuthDiv>
  <AuthDiv auth="deploy.app.view">
    <Menu.Item key="deploy">Deployment</Menu.Item>
  </AuthDiv>
</Menu>
```

### AuthFragment Component

**Location**: `src/components/AuthFragment.js`

React fragment with permission checking - doesn't add extra DOM elements.

---

## Upload Components

### DragUpload Component

**Location**: `src/components/DragUpload/index.js`

Advanced drag-and-drop file upload component with validation and custom UI support.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `onUpload` | `function` | - | Upload callback: `(fileList) => Promise` |
| `multiple` | `boolean` | `true` | Allow multiple file selection |
| `accept` | `string` | - | Accepted file types (e.g., ".js,.ts,.py") |
| `maxSize` | `number` | `0` | Max file size in MB (0 = unlimited) |
| `disabled` | `boolean` | `false` | Disable upload |
| `children` | `ReactNode` | - | Custom upload UI |
| `style` | `object` | - | Container styles |
| `className` | `string` | - | Container CSS class |

#### Features

- **Drag & Drop**: Native drag and drop support
- **File Validation**: Type and size validation with user feedback
- **Custom UI**: Support for completely custom upload interfaces
- **Progress Feedback**: Visual feedback during upload operations
- **Error Handling**: Comprehensive error messages for validation failures

#### Usage Examples

##### Basic Upload
```jsx
import { DragUpload } from '@/components';

function FileUploader() {
  const handleUpload = async (files) => {
    console.log('Uploading files:', files);
    
    // Upload logic here
    const formData = new FormData();
    files.forEach(file => formData.append('files', file));
    
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    });
    
    if (!response.ok) {
      throw new Error('Upload failed');
    }
  };

  return (
    <DragUpload
      onUpload={handleUpload}
      accept=".js,.ts,.py,.sh"
      maxSize={10} // 10MB limit
      multiple={true}
    />
  );
}
```

##### Script Upload with Custom UI
```jsx
function ScriptUploader() {
  const [uploading, setUploading] = useState(false);

  const handleUpload = async (files) => {
    setUploading(true);
    try {
      // Upload logic
      await uploadScripts(files);
      message.success(`Uploaded ${files.length} scripts successfully`);
    } catch (error) {
      message.error('Upload failed: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  return (
    <DragUpload
      onUpload={handleUpload}
      accept=".sh,.py,.js"
      maxSize={5}
      multiple={false}
    >
      <Card
        style={{ 
          textAlign: 'center', 
          cursor: 'pointer',
          border: '2px dashed #d9d9d9' 
        }}
      >
        <Icon type="cloud-upload" style={{ fontSize: 48, color: '#999' }} />
        <div style={{ marginTop: 16 }}>
          {uploading ? 'Uploading...' : 'Click or drag script files here'}
        </div>
        <div style={{ color: '#999', fontSize: 12, marginTop: 8 }}>
          Supports .sh, .py, .js files up to 5MB
        </div>
      </Card>
    </DragUpload>
  );
}
```

##### Configuration File Upload
```jsx
function ConfigUploader({ onConfigUploaded }) {
  const validateConfig = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const config = JSON.parse(e.target.result);
          if (!config.version || !config.settings) {
            reject(new Error('Invalid configuration format'));
          }
          resolve(config);
        } catch (error) {
          reject(new Error('Invalid JSON format'));
        }
      };
      reader.readAsText(file);
    });
  };

  const handleUpload = async ([file]) => {
    try {
      const config = await validateConfig(file);
      
      // Upload to server
      const formData = new FormData();
      formData.append('config', file);
      
      const response = await http.post('/api/config/upload', formData);
      
      onConfigUploaded(response);
      message.success('Configuration uploaded successfully');
    } catch (error) {
      message.error('Upload failed: ' + error.message);
    }
  };

  return (
    <DragUpload
      onUpload={handleUpload}
      accept=".json"
      multiple={false}
      maxSize={1}
    />
  );
}
```

---

## Editor Components

### ACEditor Component

**Location**: `src/components/ACEditor.js`

Advanced code editor with syntax highlighting, based on Ace Editor.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `mode` | `string` | `'text'` | Language mode (`'bash'`, `'python'`, `'javascript'`, etc.) |
| `value` | `string` | `''` | Editor content |
| `onChange` | `function` | - | Change callback: `(value) => {}` |
| `readOnly` | `boolean` | `false` | Read-only mode |
| `height` | `string` | `'300px'` | Editor height |
| `width` | `string` | `'100%'` | Editor width |
| `theme` | `string` | `'github'` | Editor theme |
| `fontSize` | `number` | `14` | Font size |
| `showGutter` | `boolean` | `true` | Show line numbers |
| `showPrintMargin` | `boolean` | `false` | Show print margin |
| `placeholder` | `string` | - | Placeholder text |

#### Usage Examples

##### Script Editor
```jsx
import { ACEditor } from '@/components';

function ScriptEditor() {
  const [script, setScript] = useState('#!/bin/bash\n\n');

  return (
    <Card title="Script Editor">
      <ACEditor
        mode="bash"
        value={script}
        onChange={setScript}
        height="400px"
        placeholder="Enter your bash script here..."
        fontSize={16}
        theme="monokai"
      />
      <div style={{ marginTop: 16 }}>
        <Button type="primary" onClick={() => saveScript(script)}>
          Save Script
        </Button>
        <Button onClick={() => testScript(script)}>
          Test Run
        </Button>
      </div>
    </Card>
  );
}
```

##### Multi-Language Editor
```jsx
function CodeEditor({ language, onLanguageChange }) {
  const [code, setCode] = useState('');

  const languageOptions = [
    { value: 'bash', label: 'Bash' },
    { value: 'python', label: 'Python' },
    { value: 'javascript', label: 'JavaScript' },
    { value: 'json', label: 'JSON' },
    { value: 'yaml', label: 'YAML' }
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Select
          value={language}
          onChange={onLanguageChange}
          style={{ width: 120 }}
        >
          {languageOptions.map(option => (
            <Select.Option key={option.value} value={option.value}>
              {option.label}
            </Select.Option>
          ))}
        </Select>
      </div>
      
      <ACEditor
        mode={language}
        value={code}
        onChange={setCode}
        height="500px"
        showGutter={true}
        showPrintMargin={true}
        theme="github"
      />
    </div>
  );
}
```

### SimpleCodeEditor Component

**Location**: `src/components/SimpleCodeEditor.js`

Lightweight code editor for simple text editing with basic syntax highlighting.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `string` | `''` | Editor content |
| `onChange` | `function` | - | Change callback |
| `placeholder` | `string` | - | Placeholder text |
| `rows` | `number` | `10` | Number of visible rows |

---

## Layout Components

### Breadcrumb Component

**Location**: `src/components/Breadcrumb.js`

Navigation breadcrumb component.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `items` | `array` | `[]` | Breadcrumb items |

### SearchForm Component

**Location**: `src/components/SearchForm.js`

Generic search form component.

---

## Utility Components

### StatisticsCard Component

**Location**: `src/components/StatisticsCard.js`

Card component for displaying statistics and metrics.

### AppSelector Component

**Location**: `src/components/AppSelector.js`

Application selection dropdown with search and filtering capabilities.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `string/number` | - | Selected application ID |
| `onChange` | `function` | - | Selection callback |
| `placeholder` | `string` | `'Select Application'` | Placeholder text |
| `allowClear` | `boolean` | `true` | Allow clearing selection |
| `showSearch` | `boolean` | `true` | Enable search functionality |

#### Usage Examples

```jsx
import { AppSelector } from '@/components';

function DeploymentForm() {
  const [selectedApp, setSelectedApp] = useState(null);

  return (
    <Form layout="vertical">
      <Form.Item label="Target Application" required>
        <AppSelector
          value={selectedApp}
          onChange={setSelectedApp}
          placeholder="Choose application to deploy"
        />
      </Form.Item>
      
      {selectedApp && (
        <Form.Item label="Environment">
          <EnvironmentSelector appId={selectedApp} />
        </Form.Item>
      )}
    </Form>
  );
}
```

### FileTree Component

**Location**: `src/components/FileTree/`

File and directory tree browser component.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `data` | `array` | `[]` | Tree data structure |
| `onSelect` | `function` | - | Node selection callback |
| `selectable` | `boolean` | `true` | Enable node selection |
| `checkable` | `boolean` | `false` | Enable checkboxes |
| `onCheck` | `function` | - | Checkbox callback |
| `expandedKeys` | `array` | `[]` | Expanded node keys |
| `onExpand` | `function` | - | Expand callback |

#### Data Format

```javascript
const treeData = [
  {
    title: 'Documents',
    key: '/documents',
    isLeaf: false,
    children: [
      {
        title: 'readme.txt',
        key: '/documents/readme.txt',
        isLeaf: true,
        size: 1024,
        modified: '2023-12-01'
      }
    ]
  }
];
```

#### Usage Examples

```jsx
import { FileTree } from '@/components';

function FileManager() {
  const [treeData, setTreeData] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);

  const handleSelect = (selectedKeys, info) => {
    const { node } = info;
    if (node.isLeaf) {
      setSelectedFile(node);
      // Load file content
      loadFileContent(node.key);
    }
  };

  const handleExpand = async (expandedKeys, { node }) => {
    if (!node.children && !node.isLeaf) {
      // Load directory contents
      const children = await loadDirectoryContents(node.key);
      updateTreeData(node.key, children);
    }
  };

  return (
    <div style={{ display: 'flex' }}>
      <div style={{ width: 300, borderRight: '1px solid #f0f0f0' }}>
        <FileTree
          data={treeData}
          onSelect={handleSelect}
          onExpand={handleExpand}
          selectable={true}
        />
      </div>
      
      <div style={{ flex: 1, padding: 16 }}>
        {selectedFile && (
          <FileViewer file={selectedFile} />
        )}
      </div>
    </div>
  );
}
```

### Link and LinkButton Components

**Location**: `src/components/Link.js`, `src/components/LinkButton.js`

Custom link components for internal navigation.

### Action Component

**Location**: `src/components/Action.js`

Generic action button component with confirmation dialogs and loading states.

### NotFound Component

**Location**: `src/components/NotFound.js`

404 error page component for handling not found routes.

---

## Component Development Guidelines

### Creating New Components

1. **File Structure**: Place components in `src/components/ComponentName/`
2. **Export**: Add to `src/components/index.js`
3. **Styling**: Use CSS modules with `.module.less` extension
4. **Props**: Document all props with PropTypes or TypeScript
5. **Testing**: Include unit tests for complex components

### Best Practices

1. **Permission Integration**: Use Auth components for access control
2. **Error Handling**: Implement proper error boundaries
3. **Accessibility**: Follow ARIA guidelines
4. **Performance**: Use React.memo for expensive components
5. **Reusability**: Design components to be reusable across different contexts

### Example Component Template

```jsx
import React, { useState, useEffect } from 'react';
import { Card, Spin } from 'antd';
import { hasPermission } from '@/libs/functools';
import styles from './index.module.less';

function MyComponent({ 
  title, 
  data, 
  onAction, 
  auth,
  loading = false,
  ...props 
}) {
  // Permission check
  if (auth && !hasPermission(auth)) {
    return null;
  }

  // Component logic here
  
  return (
    <Card 
      title={title}
      className={styles.myComponent}
      {...props}
    >
      <Spin spinning={loading}>
        {/* Component content */}
      </Spin>
    </Card>
  );
}

export default MyComponent;
```