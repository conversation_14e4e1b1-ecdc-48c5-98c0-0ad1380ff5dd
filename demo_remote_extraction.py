#!/usr/bin/env python3
"""
远程日志提取功能演示脚本
用于测试新增的远程日志提取API
"""

import requests
import json
import time

# 配置
API_BASE_URL = "http://localhost:8000/api"
LOG_PATH = "/opt/il1024_ol31744_np16_mc2.log"
PLAN_NAME = "AI推理性能测试-演示"

def test_remote_log_extraction():
    """测试远程日志提取功能"""
    
    print("🚀 开始测试远程日志提取功能")
    print("=" * 50)
    
    # 准备请求数据
    payload = {
        "log_path": LOG_PATH,
        "plan_name": PLAN_NAME,
        "server_name": "mcp1"
    }
    
    print(f"📁 日志文件路径: {LOG_PATH}")
    print(f"📋 测试计划名称: {PLAN_NAME}")
    print(f"🖥️  远程服务器: mcp1")
    print()
    
    try:
        # 发送API请求
        print("📡 正在发送API请求...")
        response = requests.post(
            f"{API_BASE_URL}/exec/remote-log-fetch/",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("✅ 日志获取成功!")
                print(f"📊 日志内容长度: {len(data['content'])} 字符")
                
                # 显示日志文件的前几行
                lines = data['content'].split('\n')[:10]
                print("\n📄 日志文件预览 (前10行):")
                print("-" * 40)
                for i, line in enumerate(lines, 1):
                    if line.strip():
                        print(f"{i:2d}: {line}")
                
                # 分析指标
                print("\n🔍 开始分析性能指标...")
                metrics = analyze_log_content(data['content'])
                
                print(f"✨ 发现 {len(metrics)} 个性能指标:")
                print("-" * 40)
                
                for metric in metrics:
                    confidence_icon = "🔥" if metric['confidence'] > 0.9 else "⭐" if metric['confidence'] > 0.8 else "📊"
                    print(f"{confidence_icon} {metric['label']}: {metric['value']} {metric['unit']} "
                          f"(置信度: {metric['confidence']:.1%}, 类别: {metric['category']})")
                
                return True
                
            else:
                print(f"❌ API返回错误: {data.get('error', '未知错误')}")
                return False
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def analyze_log_content(content):
    """
    分析日志内容，提取性能指标
    这是前端组件中相同算法的Python版本
    """
    metrics = []
    lines = content.split('\n')
    
    for index, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
            
        # 模式1: 成功请求数等计数指标
        import re
        count_pattern = r'([^:]+):\s*(\d+)\s*$'
        count_match = re.match(count_pattern, line)
        if count_match:
            label = count_match.group(1).strip()
            value = count_match.group(2)
            if any(keyword in label.lower() for keyword in ['request', 'token', 'duration']):
                metrics.append({
                    'label': label,
                    'value': value,
                    'unit': get_unit_from_label(label),
                    'confidence': 0.95,
                    'category': get_category_from_label(label),
                    'line_number': index + 1
                })
        
        # 模式2: 带小数的性能指标
        perf_pattern = r'([^:]+):\s*([0-9.,]+)\s*([A-Za-z\/°%]+)?'
        perf_match = re.match(perf_pattern, line)
        if perf_match:
            label = perf_match.group(1).strip()
            value = perf_match.group(2).replace(',', '')
            unit = perf_match.group(3) or get_unit_from_label(label)
            
            if is_performance_metric(label):
                metrics.append({
                    'label': label,
                    'value': value,
                    'unit': unit,
                    'confidence': 0.90,
                    'category': get_category_from_label(label),
                    'line_number': index + 1
                })
        
        # 模式3: GPU硬件信息
        hw_pattern = r'(GPU Model|Memory Usage|GPU Temperature|Power Consumption|Model Size|Batch Size):\s*(.+)'
        hw_match = re.match(hw_pattern, line)
        if hw_match:
            label = hw_match.group(1)
            value_with_unit = hw_match.group(2)
            # 提取数值
            value = re.search(r'([0-9.]+)', value_with_unit)
            value = value.group(1) if value else value_with_unit
            # 提取单位
            unit = extract_unit_from_value(value_with_unit)
            
            metrics.append({
                'label': label,
                'value': value,
                'unit': unit,
                'confidence': 0.95,
                'category': 'Hardware',
                'line_number': index + 1
            })
    
    return metrics

def get_unit_from_label(label):
    """根据标签推断单位"""
    lower_label = label.lower()
    if any(keyword in lower_label for keyword in ['time', 'latency', 'ttft', 'tpot', 'itl']):
        return 'ms'
    elif any(keyword in lower_label for keyword in ['throughput', 'rate']):
        return '/s'
    elif 'token' in lower_label:
        return 'tokens'
    elif 'request' in lower_label:
        return '次'
    elif 'duration' in lower_label:
        return 's'
    return ''

def get_category_from_label(label):
    """根据标签分类指标"""
    lower_label = label.lower()
    if any(keyword in lower_label for keyword in ['ttft', 'time to first token']):
        return 'Latency'
    elif any(keyword in lower_label for keyword in ['tpot', 'time per output token']):
        return 'Latency'
    elif any(keyword in lower_label for keyword in ['itl', 'inter-token latency']):
        return 'Latency'
    elif any(keyword in lower_label for keyword in ['throughput', 'rate']):
        return 'Throughput'
    elif 'request' in lower_label:
        return 'Request'
    elif 'token' in lower_label:
        return 'Token'
    elif 'duration' in lower_label:
        return 'Time'
    return 'General'

def is_performance_metric(label):
    """判断是否是性能指标"""
    keywords = ['ttft', 'tpot', 'itl', 'throughput', 'latency', 'duration', 'time', 'rate', 'mean', 'median', 'p99']
    return any(keyword in label.lower() for keyword in keywords)

def extract_unit_from_value(value):
    """从值中提取单位"""
    import re
    unit_match = re.search(r'([A-Za-z°%\/]+)', value)
    return unit_match.group(1) if unit_match else ''

if __name__ == "__main__":
    print("🎯 远程日志提取功能演示")
    print("作者: AI助手")
    print("时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    success = test_remote_log_extraction()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 演示完成! 远程日志提取功能运行正常")
    else:
        print("😞 演示失败，请检查配置和服务状态")
    
    print("\n💡 使用提示:")
    print("1. 确保后端服务正在运行 (python manage.py runserver)")
    print("2. 确保SSH连接到mcp1服务器配置正确")
    print("3. 确保日志文件路径存在且可访问")
    print("4. 在Web界面中访问 '结果展示' → '远程日志提取' 来使用GUI版本") 